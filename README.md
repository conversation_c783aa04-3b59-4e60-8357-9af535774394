# boc.inke.cn.react 
> 运营研发部h5 react版本

## 项目发布流程

### 项目启动

- 普通启动

```sh
npm run start
```

- 带[eruda](https://github.com/liriliri/eruda/blob/master/doc/README_CN.md)(移动端调试工具)启动, 
```sh
npm run start:eruda
```

### 发布测试
```sh
npm run brd:test
```
测试域名是: https:testxxx.inke.cn

测试环境url: https:testxxx.inke.cn/${devPathname}

### 发布灰度
```sh
npm run brd:gray
```

灰度域名是: https:betaxxx.inke.cn

灰度环境url: https:betaxxx.inke.cn/${devPathname}

### 发布灰度和cdn
```sh
npm run brd:cdn:gray
```

### 项目上线
> 重点来了, 流程比较复杂, 但是完全按照步骤操作就没有问题


线上域名是: https:xxx.inke.cn

线上环境url: https:xxx.inke.cn/${devPathname}


第一步: 发布cdn 和 release提交
```sh
npm run brd:onlyCdn
```

第二步: 提交merge request

1. 执行命令
```sh
npm run merge-requests
```
如果执行命令失败, 请手动点击: [merge_requests](TODO: 项目上线的merge request地址)

2. 选择release分支, 点击`Compare branches and continue`

*注意*: *这时候一定要看有多少commit 和 多少个entry的changes, `别无脑, 别无脑, 别无脑`的`submit merge request!!!`*

第三步: 线上发布

*注意*: 这个步骤最繁琐, 但也最重要, 是线上业务稳妥的关键措施

1. 去[线上发布页面](TODO: {{ 补充线上页面的deploy地址 }}), 找到最新的commit, 点击`发布`按钮, 记住: `一定不要立即点击继续按钮!!!`, `一定不要立即点击继续按钮!!!`, `一定不要立即点击继续按钮!!!`
2. 电脑配置第一台机器的hosts, 具体配置方法请参考: [配置host](https://wiki.inkept.cn/pages/viewpage.action?pageId=78063934#id-%E3%80%90%E5%89%8D%E7%AB%AF%E5%91%A8%E4%BC%9A%E3%80%9120190916%E4%BC%9A%E8%AE%AE%E7%BA%AA%E8%A6%81-%E9%85%8D%E7%BD%AEhost)
3. 验证你当前`上线页面`及`以下页面`, 一定要至少验证三个线上业务

TODO: {{ 补充当前域名下线上三个业务的页面url }}

4. 以上都每一问题了, 再点击`继续按钮`, 完成全量上线, 最后`在手机里`验证一下`当前上线页面`和`直播间其他页面`是否正常, 如果都正常, 那恭喜你, 说明你本次发布成功 ✌️

### 线上业务出问题

发现上线后线上业务有问题, 那么你不应该紧张, 也不用害怕, 也不用迷茫, 就按照下面三步来, 每个步骤都缺一不可, 佛祖和神都会保佑你:

第一件是: 立即告诉你的leader, 并在"INKE故障群里"通报问题, 然后同步`回滚` `回滚` `回滚`, [回滚地址](TODO: {{ 补充回滚地址 }}), *不会回滚一定找leader*, *不会回滚一定找leader*, *不会回滚一定找leader*, 千万千万千万别憋着

第二件是: `WEBDEV - FE大群里通报所有人不要上线`, `WEBDEV - FE大群里通报所有人不要上线`, `WEBDEV - FE大群里通报所有人不要上线`

第三件是: 冷静下来找leader沟通, 在指导下操作 master 分支 revert 到没问题的版本, 然后慢慢查找问题

5. 发布地址： https://deploy.inkept.cn/templates/deploy.html?job_name=cop.inke_owt.ops_pdl.platform_servicegroup.platform_service.family_job.h5_cluster.ali-bj

6. 新版发布地址： https://deployment.inkept.cn/release?jobName=cop.inke_owt.ops_pdl.platform_servicegroup.platform_service.family_job.h5_cluster.ali-bj

7. 测试环境发布地址: https://deployment.inkept.cn/release?jobName=cop.inke_owt.ops_pdl.platform_servicegroup.platform_service.family_job.h5_cluster.ali-test

8. 线上域名：https://mobile-family.ikbase.cn
9. 测试域名：https://testmobile-family.inkept.cn

10. 测试环境跳板机修改nginx 
    1. ssh <EMAIL>
    2. ssh ali-c-pe-testing01.bj
    3. ps -ef|grep nginx
    4. cd /a8root/bin/nginx
    5. cd conf
    6. cd conf.d/
    7. 
