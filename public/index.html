<!DOCTYPE html>
<html lang="en" data-prerendered="false" base-rem="750">
  <head>
    <%= htmlWebpackPlugin.options.htmlParams.headFirst %>
    <meta charset="utf-8" />
    <link cdn-rendered rel="shortcut icon" href="https://webcdn.inke.cn/boc/react/favicon.ico" />
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=yes" /> -->
    <meta name="keywords" content="<%= htmlWebpackPlugin.options.htmlParams.keywords %>">
    <meta name="description" content="<%= htmlWebpackPlugin.options.htmlParams.description %>">
    <!--
      manifest.json provides metadata used when your web app is added to the
      homescreen on Android. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <link cdn-rendered rel="dns-prefetch" href="//webcdn.inke.cn">
    <link cdn-rendered rel="dns-prefetch" href="//img.ikstatic.cn">
    <link cdn-rendered rel="preconnect" href="//webcdn.inke.cn">
    <link cdn-rendered rel="preconnect" href="//img.ikstatic.cn">
    <title><%= htmlWebpackPlugin.options.htmlParams.title %></title>

    <!-- eruda进行移动端调试 -->
    <% if (process.env.ERUDA) {%>
      <script src="https://static.inke.cn/boc/lib/eruda.js"></script>
      <script>eruda.init()</script>
    <% } %>

    <!-- 多entry的自定义模板处理: header标签 -->
    <%= htmlWebpackPlugin.options.htmlParams.customTemplateHeader %>
    <script>
      if(!window.Promise) { document.writeln('<script cdn-rendered src="https://webcdn.inke.cn/tpc/common/es6-promise/3.2.2/es6-promise.min.js"'+'>'+'<'+'/'+'script>'); }
    </script>
    <!-- 该属性表示已经是cdn路径, 不会再做cdn替换了 -->
    <script cdn-rendered src="https://webcdn.inke.cn/tpc/common/ik-flexible@1.1.3/ik-flexible.production.min.js"></script>
    <%= htmlWebpackPlugin.options.htmlParams.headLast %>
  </head>
  <body>
    <%= htmlWebpackPlugin.options.htmlParams.bodyFirst %>
    <!-- 自定义的 html 标签，用于屏蔽客户端内 dom 取不到的报错问题 -->
    <webview-bridge-container style="display: none;">
      <div id="ik_share_title"></div>
      <div id="ik_share_img"></div>
      <div id="ik_share_url"></div>
    </webview-bridge-container>
    <div id="root"></div>

    <!-- wenire调试, 真机使用, 可跨端 -->
    <% if (process.env.WEINRE_PORT) {%>
      <script src="http://<%= htmlWebpackPlugin.options.htmlParams.ip%>:<%= process.env.WEINRE_PORT %>/target/target-script-min.js#anonymous"></script>
    <% } %>

    <!-- react-devtools进行调试, safari端需要使用 -->
    <% if (process.env.DEVTOOLS_PORT) {%>
      <script src="http://<%= htmlWebpackPlugin.options.htmlParams.ip%>:<%= process.env.DEVTOOLS_PORT %>"></script>
    <% } %>

    <!-- 多entry的自定义模板处理 -->
    <%= htmlWebpackPlugin.options.htmlParams.customTemplate %>
    <%= htmlWebpackPlugin.options.htmlParams.scriptList %>

    <%= htmlWebpackPlugin.options.htmlParams.bodyLast %>
  </body>
</html>
