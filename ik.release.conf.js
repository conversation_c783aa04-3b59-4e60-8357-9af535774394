const { version } = require('ik-release/package.json')
const chalk = require('chalk');
const semver = require('semver')
const { spawn } = require('child_process')
const { getCliName } = require('./scripts/utils/common')

const npmCli = getCliName('npm')
const ONLINE_STATUS = process.env.SCRIPT_ENV === 'prod'
const COPY_CDN = process.env.COPY_CDN
const envMap = { // 环境变量配置
  test: {
    branch: 'test',
    maxChangesLimit: 100,
  },
  gray: {
    branch: 'gray',
    maxChangesLimit: 100,
  },
  prod: {
    branch: 'release',
    maxChangesLimit: COPY_CDN ? 10 : 100,
  }
}
const envConfig = envMap[process.env.SCRIPT_ENV]

if (semver.lt(version, '0.8.7')) {
  console.log(chalk.red(`[ik-release]版本过低, 请执行npm update ik-release, 再进行自动化提交, 谢谢`))
  console.log()
  process.exit(1)
}

module.exports = { // ik-release要读取的配置
  isNeedGitHandle: true, // 是否包含git操作 type: Boolean
  task: [
    {
      // 这块要根据具体项目而定, 不能完全复用, 不管是boc还是创新项目, 可以参考:
      // h5.inke.cn: https://code.inke.cn/opd/inke-web/h5.inke.cn/blob/master/dev/app/ik.release.conf.js
      // boc.inke.cn:
      source: 'dist', // 要copy的文件夹
      destinationFolder: '../h5.inke.cn/dist', // 目标文件夹
      destinationCwd: '../h5.inke.cn', // 目标仓库位置
      destinationBranch: envConfig.branch, // 要提交的分支
      destinationRepo: '****************:opd/inke-web/h5.inke.cn.git', // 目标仓库地址
      filter: ONLINE_STATUS ? [/\.html$/, /\favicon.ico$/, /manifest.json$/] : [],
      maxChangesLimit: envConfig.maxChangesLimit,
      onCommitEnd () {
        if (ONLINE_STATUS && !COPY_CDN) {
          spawn(npmCli, [ 'run', 'merge-requests' ], { stdio: 'inherit' })
        }
      }
    },
    COPY_CDN && {
      source: 'dist', // 要copy的文件夹
      destinationFolder: '../static-resources/h5.inke.cn/ik-h5-react', // 目标文件夹
      destinationCwd: '../static-resources', // 目标仓库位置
      destinationBranch: 'master', // 要提交的分支
      destinationRepo: '****************:tpc/sre/static-resources.git', // 目标仓库地址
      filter: [/\.(js|css|png|jpg|jpeg|gif|eot|woff|svg|ttf|ico|woff2)$/],
    },
  ].filter(Boolean)
}
