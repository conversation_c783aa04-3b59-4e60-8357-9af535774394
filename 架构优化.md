# 架构优化
> 本项目是基于create-react-app脚手架创建的项目为基础模板, 执行enject命令后进行二次搭建, 再通过ik-cli 创建&更新其他项目

## 优化详情

### 支持多entry [done]
src/views/index.js中进行配置

### 项目整体目录结构确定 [done]
详细: src

### 适配方案 **** @寒哥 [done]
vw or rem ?
不行就git上弄一个ik-flexble.js

### 跨域代理 *** @李宝旭 [done]
react-dev-uitls目前不支持webpack.devServer.proxy配置, 需要改动react-dev-uitls源码, 我再详细看一下确定方案

### eslint *** @李宝旭 [done]
支持运行时校验, 不然新人会使项目越来越乱

### ik-antd-mobile *** @子寒 [done]
单独git仓库, [仓库地址](), 修改为映客主题色, 与移动端适配融合, 与子寒沟通好

### redux, react-router *** @李宝旭 [done]
在src/pages/index/index.js中run起来
需要支持broswerHistory 和 hashHistory两种方案
其中broswerHistory需要更改nginx配置, 如果是新域名的项目, 首页: broswerHistory, 如果是发布到其他域名下, 首选hashHistory

todo: 映客app内的hashHistory
todo: module.hot之后 redux重新渲染组件的mapStateToprops

### ik-weixin集成 *** @寒哥 [done]
独立git仓库, [仓库地址](), 使用ik-modules-workflow创建


### build支持 ** @李宝旭 [done]
自定义publicPath
build依赖分析
自定义index.html其他内容
与预渲染结合

### 部署脚本优化 *** @王合亮
独立git, [仓库地址]()
1 主要是copy方面的, copy到cdn,可以多传一点, copy到具体, 过滤非cdn文件, 如只copy *.html, service-worker.js
2 后面这个copy方案要足够通用, 上传到gitlib, 通过npm安装, 不同项目可以共享, 到时候放到node_modules下,
  暂定功能: 
  1) 支持传入参数(from , to), 而且要是一个数组, 里面有多个copy任务
  2) 每次copy, 都支持过滤不同文件类型, 哪些不拷贝


### 核心库 配置外链 *** @李宝旭 [done]
react, react-dom, redux, react-router, axios
据说libDll插件已经过时了, 不知道现在什么方案? 晖哥调研一下


### entry在控制台输出 *** @李宝旭 [done]
react-dev-utils只是支持到域名的输出, 不能具体到不同entry, 需要改源码或是加个插件


TODO:
## 优化方面

### 预渲染方案 *** @李宝旭
首屏dom, 需要构建到 *.html中, 页面打开效果得到显著提升

遇到问题:
  路由的情况: 
    1) HashRouter怎么办?
    2) BrowserRouter下面的多个路由, 每个路由都需要配置nginx的location才能保证页面的正常加载

### mock方案 ** @李宝旭
TODO: 待讨论
打包时剔除掉 .mock.data.js, 保证线上js足够小

### service-worker支持 ** @李子寒
如boc就挺好

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).

### Code Splitting

This section has moved here: https://facebook.github.io/create-react-app/docs/code-splitting

### Analyzing the Bundle Size

This section has moved here: https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size

### Making a Progressive Web App

This section has moved here: https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app

### Advanced Configuration

This section has moved here: https://facebook.github.io/create-react-app/docs/advanced-configuration

### Deployment

This section has moved here: https://facebook.github.io/create-react-app/docs/deployment

### `npm run build` fails to minify

This section has moved here: https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify


