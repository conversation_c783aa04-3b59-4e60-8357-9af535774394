{
  "compilerOptions": {
    "experimentalDecorators": true,
    "baseUrl": ".",
    "paths": {
      "@*": ["./src/*"],
      "src/*": ["./src/*"],
      "pages": ["./src/pages"],
      "bridgex": ["./src/bridgex"],
      "entry": ["./src/entry"],
      "styles": ["./src/styles"],
      "config": ["./src/config"],
      "components": ["./src/components"],
      "mocks": ["./src/mocks"],
      "resources": ["./src/resources"],
      "utils": ["./src/utils"],
      "decorator": ["./src/decorator"],
      "pages/*": ["./src/pages/*"],
      "bridgex/*": ["./src/bridgex/*"],
      "entry/*": ["./src/entry/*"],
      "styles/*": ["./src/styles/*"],
      "config/*": ["./src/config/*"],
      "components/*": ["./src/components/*"],
      "mocks/*": ["./src/mocks/*"],
      "resources/*": ["./src/resources/*"],
      "utils/*": ["./src/utils/*"],
      "decorator/*": ["./src/decorator/*"],
    }
  },
  "exclude": ["node_modules", "dist"]
}
