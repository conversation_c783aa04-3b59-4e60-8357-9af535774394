module.exports = {'demo': {'title': 'demo', 'description': '最牛逼的demo', 'prerenderOptions': {'routes': ['/']}, 'customTemplateHeader': ' <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=yes" />', 'injectScript': ['//webstatic.inke.cn/static/lib/jweixin-1.0.0.js'], 'baseSize': 750, 'isPx2rem': true}, 'home': {'title': '', 'description': 'Family首页', 'prerenderOptions': {'routes': ['/home']}, 'customTemplateHeader': ' <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=yes" />', 'baseSize': 750, 'isPx2rem': true}, 'order': {'title': '', 'description': '点餐中心', 'prerenderOptions': {'routes': ['/order']}, 'customTemplateHeader': ' <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=yes" />', 'baseSize': 750, 'isPx2rem': true}, 'visitor': {'title': '', 'description': '访客预约', 'prerenderOptions': {'routes': ['/visitor']}, 'customTemplateHeader': ' <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=yes" />', 'baseSize': 750, 'isPx2rem': true}, 'flow': {'title': '', 'description': '流程审批', 'prerenderOptions': {'routes': ['/']}, 'customTemplateHeader': ' <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=yes" />', 'baseSize': 750, 'isPx2rem': true}, 'skip': {'title': '', 'description': '跳转', 'prerenderOptions': {'routes': ['/skip']}, 'customTemplateHeader': ' <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=yes" />', 'baseSize': 750, 'isPx2rem': true}, 'asset': {'title': '', 'description': '资产管理', 'prerenderOptions': {'routes': ['/asset']}, 'customTemplateHeader': ' <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=yes" />', 'baseSize': 750, 'isPx2rem': true}}
