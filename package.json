{"name": "ik-h5-react", "version": "0.1.0", "private": true, "scripts": {"dev": "npm run start", "start": "cross-env-shell PORT=8000 node scripts/start.js", "start:custom-serve-path": "cross-env-shell PORT=8000 DEV_PUBLIC_URL=/custom-serve-path node scripts/start.js", "start:eruda": "cross-env-shell ERUDA=true npm run start", "start:weinre": "cross-env-shell WEINRE_PORT=8001 npm run start", "start:devtools": "cross-env-shell DEVTOOLS_PORT=8097 npm run start", "weinre": "weinre --httpPort 8001 --boundHost -all-", "react-devtools": "react-devtools", "merge-requests": "open-cli https://code.inke.cn/opd/activitys/boc.inke.cn/merge_requests/new", "build": "cross-env-shell GENERATE_SOURCEMAP=false node scripts/build.js", "build:custom-serve-path": "cross-env-shell GENERATE_SOURCEMAP=false DIST_SERVE_PATH=/custom-serve-path PUBLIC_URL=/custom-serve-path node scripts/build.js", "build:cdn": "cross-env-shell GENERATE_SOURCEMAP=false CDN_PATH=https://webcdn.inke.cn/ik-h5-react/ node scripts/build.js", "build:custom-serve-path:cdn": "cross-env-shell GENERATE_SOURCEMAP=false DIST_SERVE_PATH=/custom-serve-path CDN_PATH=https://webstatic.inke.cn/ik-h5-react/ node scripts/build.js", "build:debug": "cross-env-shell MINIMIZE=false BUILD_WATCH=true node scripts/build.js", "build:debug:nosourcemap": "cross-env-shell BUILD_WATCH=true GENERATE_SOURCEMAP=false MINIMIZE=false node scripts/build.js", "dist-server": "node scripts/distServer.js --dist-root=dist", "dist-server:open": "node scripts/distServer.js --dist-root=dist --open", "dist-server:custom-root-dist": "node scripts/distServer.js --dist-root=.tmp-dist", "live-dist-server": "node scripts/liveDistServer.js --dist-root=dist", "release:test": "cross-env SCRIPT_ENV=test ik-release --config ik.release.conf", "release:gray": "cross-env SCRIPT_ENV=gray ik-release --config ik.release.conf", "release:gray:cdn": "cross-env SCRIPT_ENV=gray COPY_CDN=true ik-release --config ik.release.conf", "release:online": "cross-env SCRIPT_ENV=prod ik-release --config ik.release.conf", "release:online:cdn": "cross-env SCRIPT_ENV=prod COPY_CDN=true ik-release --config ik.release.conf", "basedeploy": "cross-env-shell GENERATE_SOURCEMAP=false DIST_SERVE_PATH=/custom-serve-path PUBLIC_URL=/custom-serve-path node scripts/deploy.js", "cdndeploy": "cross-env-shell COPY_CDN=true CDN_PATH=https://webcdn.inke.cn/h5.inke.cn/ik-h5-react/ npm run basedeploy", "brd:test": "cross-env-shell SCRIPT_ENV=test MINIMIZE=false TARGET_HOST=https://testh5.inke.cn npm run basedeploy", "brd:gray": "cross-env-shell SCRIPT_ENV=gray MINIMIZE=false TARGET_HOST=https://betah5.inke.cn npm run basedeploy", "brd:gray:cdn": "cross-env-shell SCRIPT_ENV=gray MINIMIZE=false TARGET_HOST=https://betah5.inke.cn npm run cdndeploy", "brd:onlyCdn": "cross-env-shell SCRIPT_ENV=prod TARGET_HOST=https://h5.inke.cn npm run cdndeploy", "br:preOnline": "cross-env-shell SCRIPT_ENV=prod TARGET_HOST=https://h5.inke.cn npm run basedeploy"}, "devDependencies": {"@babel/core": "7.1.6", "@babel/plugin-proposal-class-properties": "^7.3.0", "@babel/plugin-proposal-decorators": "^7.3.0", "@babel/plugin-proposal-export-default-from": "^7.2.0", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/plugin-transform-runtime": "^7.7.4", "@babel/polyfill": "^7.4.4", "@babel/preset-env": "^7.7.4", "@inkefe/create-decorator": "^0.1.4", "@svgr/webpack": "2.4.1", "antd-mobile-icons": "^0.3.0", "axios": "0.18.0", "axios-service": "^1.4.4", "babel-core": "6.26.3", "babel-eslint": "9.0.0", "babel-jest": "23.6.0", "babel-loader": "8.0.4", "babel-plugin-import": "^1.11.0", "babel-plugin-lodash": "^3.3.4", "babel-plugin-named-asset-import": "^0.3.0", "babel-plugin-ramda": "^2.1.0", "babel-preset-react-app": "^7.0.0", "bfj": "6.1.1", "body-parser": "^1.19.0", "calendar-lite": "^0.1.0", "case-sensitive-paths-webpack-plugin": "2.1.2", "chalk": "2.4.1", "cheerio": "^1.0.0-rc.2", "cli-table3": "^0.5.1", "connect": "^3.6.2", "core-js": "^3.4.7", "cross-env": "^5.2.0", "css-loader": "1.0.0", "dotenv": "6.0.0", "dotenv-expand": "4.2.0", "eslint": "5.6.0", "eslint-config-airbnb": "^17.1.0", "eslint-config-react-app": "^3.0.6", "eslint-config-standard": "^12.0.0", "eslint-friendly-formatter": "^4.0.1", "eslint-loader": "2.1.1", "eslint-plugin-flowtype": "2.50.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-jsx-a11y": "6.1.2", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-react": "7.11.1", "eslint-plugin-react-hooks": "^2.3.0", "eslint-plugin-standard": "^4.0.0", "file-loader": "2.0.0", "fork-ts-checker-webpack-plugin-alt": "0.4.14", "fs-extra": "7.0.0", "hash-sum": "^2.0.0", "hoist-non-react-statics": "^3.2.1", "html-webpack-plugin": "4.0.0-alpha.2", "http-proxy-middleware": "^0.19.1", "identity-obj-proxy": "3.0.0", "ik-antd": "git+ssh://****************:opd/fe-aws/ik-antd.git", "ik-antd-mobile": "git+ssh://****************:opd/fe-aws/modules-ui/ik-antd-mobile.git", "ik-bridgex": "git+ssh://****************:opd/fe-aws/modules/ik-bridgex.git", "ik-release": "git+ssh://****************:opd/fe-aws/modules/ik-release.git", "ik-weixin": "git+ssh://****************:opd/fe-aws/modules/ik-weixin.git", "jest": "23.6.0", "jest-pnp-resolver": "1.0.1", "jest-resolve": "23.6.0", "less": "^3.9.0", "less-loader": "5.0.0", "live-server": "^1.2.1", "lodash": "^4.17.11", "mini-css-extract-plugin": "0.4.3", "minimist": "^1.2.0", "mockjs": "^1.0.1-beta3", "node-sass": "^4.11.0", "open": "^6.4.0", "open-cli": "^5.0.0", "optimize-css-assets-webpack-plugin": "5.0.1", "ora": "^3.4.0", "pnp-webpack-plugin": "1.1.0", "postcss-flexbugs-fixes": "4.1.0", "postcss-loader": "3.0.0", "postcss-preset-env": "6.3.1", "postcss-px2remvw": "^1.0.0", "postcss-safe-parser": "4.0.1", "prerender-spa-plugin": "^3.4.0", "prop-types": "^15.6.2", "rc-form": "^2.4.1", "react": "16.8.6", "react-app-polyfill": "^0.2.0", "react-dev-utils": "^7.0.1", "react-devtools": "^3.6.0", "react-dom": "16.8.6", "react-hot-loader": "^4.6.3", "react-redux": "7.1.0", "react-router": "4.3.1", "react-router-dom": "^5.2.0", "redux": "4.0.1", "redux-logger": "^3.0.6", "redux-thunk": "2.3.0", "release-deploy-webpack-plugin": "git+ssh://****************:opd/fe-aws/modules/release-deploy-webpack-plugin.git", "resolve": "1.8.1", "sass-loader": "7.1.0", "script-ext-html-webpack-plugin": "^2.1.4", "service-mock-middleware": "^1.3.4", "service-proxy-middleware": "^1.0.3", "style-loader": "0.23.0", "terser-webpack-plugin": "1.4.2", "url-loader": "1.1.1", "webpack": "4.19.1", "webpack-build-notifier": "^1.0.1", "webpack-dev-server": "3.1.14", "webpack-manifest-plugin": "2.0.4", "weinre": "^2.0.0-pre-I0Z7U9OV", "workbox-webpack-plugin": "3.6.3"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "proxy": "", "dependencies": {"antd-mobile-v5": "npm:antd-mobile@^5.15.1", "dingtalk-jsapi": "^2.13.9"}}