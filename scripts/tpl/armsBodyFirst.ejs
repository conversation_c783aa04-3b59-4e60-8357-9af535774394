<script>
  !(function(){
    if (document.documentElement.getAttribute('data-prerendered') !== 'true') return;

    var blResolve, blReject;
    var blPromise = new Promise(function (resolve, reject) {
      blResolve = resolve;
      blReject = reject;
    });

    var slice = Array.prototype.slice;
    var toArray = function (obj) {
      return slice.call(obj);
    }

    window.__ikBl = window.__ikBl || {}
    window.__ikBl.invoke = function ikBlInvoke() {
      var args = toArray(arguments);
      var methodName = args.shift();
      return blPromise.then(function () {
        var func = window.__bl[methodName];
        if (typeof func === 'function') {
          return func.apply(window.__bl, args);
        }
      })
    };

    // 支持业务异步更改uid场景
    window.__ikBl.setUid = function ikBlSetUid (uid) {
      window.__ikBl.invoke('setConfig', { uid: uid })
      window.__ikBl.invoke('msg', 'update uid')
    };

    var uid = (/(\?|&)uid=(\d+)/.exec(window.location.search || window.location.hash) || { 2: null })[2];
    var uuid = (/INKE_UUID=(\S+);/.exec(window.document.cookie) || { 1: null })[1];
    var cv = (/(\?|&)cv=((\w|\.)+)/.exec(window.location.href) || { 2: undefined })[2];
    window.__bl || (window.__bl = {});
    window.__bl.config = {
      uid: uid || uuid,
      tag: uuid,
      release: cv,
      <%= config %>
    };

    // 处理收集的资源加载异常数据
    window.addEventListener('DOMContentLoaded', function () {
      if (window.__sourceError && window.__sourceError.length && window.__sourceError.length > 0) {
        window.__sourceError.forEach(function (item) {
          window.__ikBl.invoke('error', new Error('发生了一个资源加载的错误'), { filename: item.target.outerHTML || item.target.src, });
          window.__ikBl.invoke('sum', 'static-error-404', 1);
        })
      }
    });

    function insertBlScript() {
      var body = document.body;
      var script = document.createElement('script');
      var BL_URL = 'https://webcdn.inke.cn/tpc/common/bl@1.0.0/bl.min.js';
      script.setAttribute('crossorigin', '');
      script.onload = blResolve;
      script.src = BL_URL;
      body.insertBefore(script, body.firstChild);
    };

    insertBlScript();
  })();
</script>
