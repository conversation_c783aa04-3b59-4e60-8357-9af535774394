import { HOST_BETA, HOST_ONLINE, HOST_TEST, HOST_DEV } from './constants'

// const { location: { host: HOST, origin: ORIGIN } } = window
const { location: { host: HOST } } = window
export const IS_DEV = process && process.env.NODE_ENV === 'development'

export const HOST_ENV = [HOST_BETA, HOST_ONLINE, HOST_TEST].find(key => HOST.indexOf(key) === 0) || HOST_DEV

export const prodRootMap = {
  [HOST_ONLINE]: {
    baseapi: '//baseapi.busi.inke.cn/',
    familyRoot: '//mobile-busi.ikbase.cn/',
    familyWorkFlow: '//mobile-busi.ikbase.cn/',
    assetRoot: '//assets.inkept.cn/'
  },
  [HOST_BETA]: {
    baseapi: '//betabaseapi.busi.inke.cn/',
    familyRoot: '//betafamily.busi.inkept.cn/',
    familyWorkFlow: '//test-ops-platform-workflow.inkept.cn/',
    assetRoot: '//beta-assets.inkept.cn/'
  },
  [HOST_TEST]: {
    baseapi: '//testbaseapi.busi.inke.cn/',
    familyRoot: '//testfamily.busi.inkept.cn/',
    familyWorkFlow: '//test-ops-platform-workflow.inkept.cn/',
    assetRoot: '//test-assets.inkept.cn/'
  }
}

export const prodApi = prodRootMap[HOST_ONLINE]

const CUR_HOST = Object.keys(prodRootMap).find((key) => {
  // 将字符串转为正则, 找到"匹配索引"为0的 host
  // 这样key就可以支持正则写法了, 如: 'h5.inke.(cn|com)' 就可以 既匹配h5.inke.cn又匹配h5.inke.com了
  const match = HOST.match(new RegExp(key))
  return match && (match.index === 0)
}) || HOST_ONLINE

const rootObj = prodRootMap[CUR_HOST] || {}

const DEFAULT_ROOT = '/'

// 再root很多的场景, 非常适用
const formatAPIS = fn => Object.keys(rootObj).reduce((obj, host) => {
  if (host) {
    obj[host] = fn(rootObj[host])
  }
  return obj
}, {})

const PROD = {
  APIS: formatAPIS(rootStr => rootStr || DEFAULT_ROOT)
}

const DEV = {
  APIS: {
    ...formatAPIS(() => DEFAULT_ROOT),
    // 其他apiRootKey覆盖上面的
  }
}

const ENV = IS_DEV ? DEV : PROD
export default ENV.APIS
