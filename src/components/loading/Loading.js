/**
 * 全局loading
 *
 * @params {String} borderColor
 * @params {Boolean} visible
 */
import React from 'react'
import ReactDOM from 'react-dom'
import PropTypes from 'prop-types'
import './Loading.less'

const classPrefix = 'boc-react-loading'
const destroyFns = []
const IS_REACT_16 = !!ReactDOM.createPortal;

class InKeLoading extends React.Component {
  static propsTypes = {
    visible: PropTypes.bool,
    borderColor: PropTypes.string,
    animateName: PropTypes.string,
  }

  static defaultProps = {
    visible: false,
    animateName: 'snake'
  }

  constructor(props) {
    super(props)

    this.state = {}
  }

  componentDidMount() {

  }

  render() {
    const { borderColor, visible, animateName } = this.props
    let borderStyle
    if (this.props.borderColor) {
      borderStyle = { borderTopColor: borderColor, borderLeftColor: borderColor, borderBottomColor: borderColor }
    }
    return <div className={`${classPrefix}`} style={{ display: `${visible ? 'block' : 'none'}` }} >
      <div className={animateName} style={borderStyle}></div>
    </div>
  }
}

const LOADING_DOM_ID = 'inke-loading-root'

const LoadingView = {
  hide () {},

  show (props) {
    let div = this.div || document.getElementById(LOADING_DOM_ID)
    if (!div) {
      const firstChild = document.body.children[0]
      div = document.createElement('div')
      div.setAttribute('id', LOADING_DOM_ID)
      if (firstChild) {
        document.body.insertBefore(div, firstChild)
      } else {
        document.body.appendChild(div)
      }
    }
    this.div = div
    let currentConfig = { ...props, close, visible: true }

    this.hide = close.bind(this)

    function close (...args) {
      currentConfig = { ...props, visible: false, afterClose: destroy.bind(this, ...args) }

      if (IS_REACT_16) {
        render(currentConfig);
      } else {
        destroy(...args);
      }
    }

    function update (newConfig) {
      currentConfig = {
        ...currentConfig,
        ...newConfig,
      };
      render(currentConfig);
    }

    function destroy (...args) {
      const unmountResult = ReactDOM.unmountComponentAtNode(div)
      if (unmountResult && div.parentNode) {
        div.parentNode.removeChild(div)
      }

      for (let i = 0; i < destroyFns.length; i++) {
        const fn = destroyFns[i];
        if (fn === close) {
          destroyFns.splice(i, 1);
          break;
        }
      }
    }

    function render (props) {
      ReactDOM.render(<InKeLoading { ...props }/>, div)
    }

    render(currentConfig)

    destroyFns.push(close)

    return {
      destroy: close,
      update
    }
  }
}

export default LoadingView
