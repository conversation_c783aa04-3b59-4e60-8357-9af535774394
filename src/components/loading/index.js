/**
 * 全局loading效果
 *
 * @example
 *
 * import loading from 'components/loading'
 * loading.show()
 * loading.hide()
 */

import LoadingView from './Loading'
import LoadingManager from './loading-manager';

const loadingManager = new LoadingManager()
const DEFAULT_TIMEOUT = 300
let timer

const show = (props) => {
  const timeout = (props && props.timeout !== undefined) ? props.timeout : DEFAULT_TIMEOUT

  // loadingManager.show()
  // LoadingView.show(props)

  if (timer) {
    clearTimeout(timer)
  }

  if (timeout && typeof timeout === 'number') {
    // 超时保护
    timer = setTimeout(() => {
      if (!loadingManager.isQueueClear) {
        loadingManager.clearQueue()
        LoadingView.hide()
      }
    }, timeout)
  }
}

const hide = () => {
  loadingManager.hide()
  if (loadingManager.isQueueClear) {
    LoadingView.hide()
  }
}

export default {
  show,
  hide
}
