@import '~src/styles/variable.less';

@class-prefix: ~'boc-react-loading';

.@{class-prefix} {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  z-index: 1000;
  width: 60px;
  height: 60px;

  .snake {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border-style: solid;
    border-width: 5px 5px 0px 0px;
    border-color: @var-theme-color transparent transparent @var-theme-color;
    -webkit-animation: mint-spinner-rotate 1s infinite linear;
    animation: mint-spinner-rotate 1s infinite linear;
  }

}

@-webkit-keyframes mint-spinner-rotate {
  0% {
      -webkit-transform: rotate(0deg);
              transform: rotate(0deg);
  }
  100% {
      -webkit-transform: rotate(360deg);
              transform: rotate(360deg);
  }
}
@keyframes mint-spinner-rotate {
  0% {
      -webkit-transform: rotate(0deg);
              transform: rotate(0deg);
  }
  100% {
      -webkit-transform: rotate(360deg);
              transform: rotate(360deg);
  }
}
