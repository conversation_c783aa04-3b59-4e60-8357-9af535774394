.confirm-wrapper {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFang SC,Helvetica Neue,Hiragino Sans GB,Helvetica,Microsoft YaHei,Arial;
  .confirm-content {
    background-color: #fff;
    border: 0;
    border-radius: 10px;
    width: 480px;
  }
  .confirm-title {
    text-align: center;
    padding: 30px 0;
    font-weight: bolder;
    font-size: 30px;
  }
  .confirm-main {
    text-align: center;
    padding-bottom: 20px;
  }
  .confirm-bottom {
    height: 90px;
    line-height: 90px;
    font-family: PingFang SC,Helvetica Neue,Hiragino Sans GB,Helvetica,Microsoft YaHei,Arial;
    display: flex;
    font-size: 24px;
    border-top: 1px solid #ddd;
    color: #333;
    font-weight: 900;
    .confrim-bottom-cancel {
      flex: 1;
      text-align: center;
      border-right: 1px solid #ddd;
    }
    .confrim-bottom-sure {
      flex: 1;
      text-align: center;
      color: #00C8B7;
    }
  }
}
.toast-wrapper {
  position: fixed;
  font-family: PingFang SC,Helvetica Neue,Hiragino Sans GB,Helvetica,Microsoft YaHei,Arial;
  z-index: 999;
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  top: 50%;
  left: 50%;
  width:50%;
  transform: translateX(-50%) translateY(-50%);
  padding: 20px 40px;
  text-align: center;
  font-weight: bolder;
}