import React, { useState, useEffect } from 'react'
import ReactDOM from 'react-dom'
import './index.less'
let toastTime = null

const Confirm = (props = {}) => {
  const [message, setMessage] = useState({})
  useEffect(() => {
    setMessage(props)
  }, [props])

  function deleteDom () {
    document.getElementsByTagName('body')[0].removeChild(document.getElementById('my-modal'))
  }

  function cancelFun () {
    deleteDom()
    message.paramObj && message.paramObj.cancelCb()
  }
  function sureFun () {
    deleteDom()
    message.paramObj && message.paramObj.sureCb()
  }

  return <div className='confirm-wrapper'>
    <div className='confirm-content'>
      <div className='confirm-title'>{message.paramObj && message.paramObj.title}</div>
      <div className='confirm-main'>{message.paramObj && message.paramObj.content}</div>
      <div className='confirm-bottom'>
        <span className='confrim-bottom-cancel' onClick={cancelFun}>取消</span>
        <span className='confrim-bottom-sure' onClick={sureFun}>确认</span>
      </div>
    </div>
  </div>
}

const ToastMessage = (messge) => {
  const div = document.createElement('div')
  div.setAttribute('id', 'my-toast')
  document.getElementsByTagName('body')[0].appendChild(div)
  if (toastTime) clearTimeout(toastTime)
  toastTime = setTimeout(() => {
    document.getElementsByTagName('body')[0].removeChild(document.getElementById('my-toast'))
  }, 1000);
  return ReactDOM.render(<div className='toast-wrapper'>
    {messge}
  </div>, document.getElementById('my-toast'))
}

const showConfirm = (title = '标题', content = '内容', cancelCb = () => {}, sureCb = () => {}) => {
  const div = document.createElement('div')
  div.setAttribute('id', 'my-modal')
  document.getElementsByTagName('body')[0].appendChild(div)
  const obj = {
    title,
    content,
    cancelCb,
    sureCb
  }
  return ReactDOM.render(<Confirm paramObj={obj}></Confirm>, document.getElementById('my-modal'))
}

export default {
  showConfirm,
  ToastMessage
}
