.people-popup {
  .people-popup-body {
    height: 60vh;
  }
  .select-people-popup {
    width: 100%;
    overflow: hidden;
    .operation-btn {
      display: flex;
      height: 88px;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #eee;
      padding: 4px;
      .btn-color {
        color: #25e6d2;
      }
    }
    .people-content {
      .search {
        margin: 10px 20px;
      }
      .people-list {
        height: calc(60vh - 100px);
        overflow-y: scroll;
        color: #000000;
        border-top: 1px solid #eee;
        a {
          color: #000000;
        }
        .adm-check-list-item-active, .adm-check-list-item-extra {
          color: #25e6d2;
        }
      }
    }
    .people-popup-title {
      font-size: 36px;
    }
  }
}
