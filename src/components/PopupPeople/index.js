/**
 * 选择人员
 *
 * selectedValueFormat: 指定选中人员的具体属性
 * 没有传，或者传的不是字符串，或者传的不是人员含有的属性，默认会返回选中人完整的信息
 * 否则返回具体的属性信息
 * 例：selectedValueFormat='email' 返回选中人邮箱
 *
 */
import React, { memo, useEffect, useMemo, useState, useCallback, useRef } from 'react'
import { Popup, Empty, CheckList, Button } from 'antd-mobile-v5'
import { SearchBar } from 'antd-mobile'
import { cloneDeep, isEmpty } from 'lodash'
import './index.less'

const PopupPeople = (props) => {
  const {
    visible = false,
    title = '请选择人员',
    value,
    selectedValueFormat, // 指定选中人员的具体属性，无则返回选中人员完整信息
    allPeople = [],
    onCancal = () => {},
    onOk = () => {},
  } = props

  const searchBarRef = useRef(null)
  const [showPeopleList, setShowPeopleList] = useState([]) // 显示人员列表
  const [selectedPeople, setSelectedPeople] = useState([]) // 选中人员

  // 是否指定返回选中人员信息具体字段
  const getIsSpecificField = useCallback(() => {
    const result = (
      selectedValueFormat &&
      typeof selectedValueFormat === 'string' &&
      (!isEmpty(allPeople) && allPeople[0].hasOwnProperty(selectedValueFormat))
    )
    return result
  }, [selectedValueFormat, allPeople])

  const handleSelectedOk = useCallback(
    (val) => {
      // const selectedPeopleValue = val || selectedPeople
      // let selectedResult = selectedPeopleValue.toString()
      let selectedResult = val || selectedPeople
      const isSpecificField = getIsSpecificField()
      if (!isSpecificField) { // 返回选中人完整信息
        selectedResult = (
          !isEmpty(selectedResult) &&
          !isEmpty(allPeople) &&
          allPeople.filter(item => selectedResult.includes(item.login_name))
        )
      }
      onOk(selectedResult)
    },
    [selectedPeople, allPeople, getIsSpecificField, onOk]
  )

  const handleSelect = useCallback(
    (value) => {
      setSelectedPeople(value)
      !isEmpty(value) && handleSelectedOk(value)
    },
    [handleSelectedOk]
  )

  const handleSearchChange = useCallback(
    (val) => {
      let resultArr = []
      if (val) {
        allPeople.length && allPeople.forEach(item => {
          if ((item.real_name.indexOf(val) !== -1 || item.login_name.indexOf(val) !== -1) && resultArr.length < 30) {
            resultArr.push(item)
          }
        })
      } else {
        resultArr = allPeople.length > 100 ? allPeople.slice(0, 100) : allPeople
      }
      setShowPeopleList(resultArr)
    },
    [allPeople]
  )

  const onConfirm = () => {
    handleSelectedOk()
  }

  useEffect(() => {
    if (!isEmpty(allPeople)) {
      const data = cloneDeep(allPeople)
      const arr = data.length > 100 ? data.slice(0, 100) : data
      setShowPeopleList(arr)
    }
  }, [allPeople])

  useEffect(() => {
    if (typeof searchBarRef.current?.onBlur === 'function') {
      searchBarRef.current.onBlur()
    }
    let selectedResult = []
    if (Array.isArray(value)) {
      selectedResult = value
    } else {
      selectedResult = value ? [value] : []
    }
    setSelectedPeople(selectedResult)
  }, [visible, value])

  return <div className='popup-people'>
    <Popup
      visible={visible}
      onMaskClick={onCancal}
      className='people-popup'
      bodyClassName='people-popup-body'
    >
      <div className='select-people-popup'>
        <div className='operation-btn'>
          <Button
            className='btn-color'
            fill='none'
            onClick={onCancal}
          >
            取消
          </Button>
          <span className='people-popup-title'>{title}</span>
          <Button
            className='btn-color'
            fill='none'
            onClick={onConfirm}
          >
            确认
          </Button>
        </div>
        <div className='people-content'>
          <SearchBar
            ref={searchBarRef}
            className='search'
            placeholder="请输入姓名搜索"
            onChange={handleSearchChange}
          />
          {
            useMemo(
              () => (
                showPeopleList.length ? (
                  <CheckList
                    className='people-list'
                    value={selectedPeople}
                    onChange={handleSelect}
                  >
                    {
                      showPeopleList.map((item, index) => (
                        <CheckList.Item
                          key={index}
                          value={getIsSpecificField() ? item[selectedValueFormat] : item.login_name}
                        >
                          {`${item.real_name}-${item?.dept[0]?.name}-${item.email}`}
                        </CheckList.Item>
                      ))
                    }
                  </CheckList>
                ) : (<Empty describe='暂无数据' />)
              ),
              [
                selectedPeople,
                showPeopleList,
                selectedValueFormat,
                handleSelect,
                getIsSpecificField
              ]
            )
          }
        </div>
      </div>
    </Popup>
  </div>
}

export default memo(PopupPeople)
