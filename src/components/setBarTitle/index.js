import * as dd from 'dingtalk-jsapi'
import { Toast } from 'antd-mobile'

const setTitle = (title = '') => {
  if (dd.env.platform === 'notInDingTalk') {
    return
  }
  dd.ready(function() {
    // dd.ready参数为回调函数，在环境准备就绪时触发，jsapi的调用需要保证在该回调函数触发后调用，否则无效。
    dd.biz.navigation.setTitle({
      title: title,
      onSuccess: function (res) {
        // Toast.success('Load success !!!', 1)
        console.log('title set success')
      },
      onFail: function(err) {
        Toast.fail(err, 1)
      }
    })
  })
}
export default setTitle
