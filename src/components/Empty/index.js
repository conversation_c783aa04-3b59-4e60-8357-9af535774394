/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-02 14:30:00
 * @LastEditTime: 2020-10-15 20:28:47
 * @LastEditors: Please set LastEditors
 * @Description: 页面 Top
 */
import React, { PureComponent } from 'react'
import './index.less'

class Empty extends PureComponent {
  render () {
    const {
      describe,
      className,
    } = this.props
    return (
      <div className={className ? `${className}` : 'empty'}>
        <div className='empty-img'></div>
        <span className='describe'>{describe}</span>
      </div>
    )
  }
}

export default Empty
