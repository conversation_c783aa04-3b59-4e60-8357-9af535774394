/**
 * 扫码按钮组件
 * 接入钉钉扫码功能
 */
import React, { memo } from 'react'
import { Button } from 'antd-mobile-v5'
import './index.less'

const ScanButton = (props) => {
  return <div className='scan-button'>
    <Button
      {...props}
      shape='default'
      color='primary'
      className='scan-button-wrapper'
    >
      <img src='https://img.ikstatic.cn/MTY1MDQ1MDAxNDgwMyMxOTcjcG5n.png' className='scan-code-icon' alt='' />
      扫码盘点
    </Button>
  </div>
}

export default memo(ScanButton)
