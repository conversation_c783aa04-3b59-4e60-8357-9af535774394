/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-02 14:30:00
 * @LastEditTime: 2020-11-20 20:28:47
 * @LastEditors: Please set LastEditors
 * @Description: 页面 Top
 */
import React, { PureComponent } from 'react'
import * as dd from 'dingtalk-jsapi'
import './index.less'

class NavTopBar extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {};
  }

  closePage = () => {
    if (!dd.other) {
      dd.ready(function() {
        dd.biz.navigation.close({
          onSuccess: function (res) {
            console.log('关闭成功')
          },
          onFail: function (err) {
            console.error(err)
          }
        })
      })
    }
  }

  gobackFun = () => {
    if (this.props.backRouter) { // 回退上一步
      this.props.history.goBack();
    } else if (this.props.JumpUrl) { // 路径跳转
      window.location.href = this.props.JumpUrl
    } else if (this.props.BackHomePage) { // 返回首页
      this.props.backNowFirstPage()
    }
  }

  render () {
    const {
      title
    } = this.props
    return (
      <div className='nav-top-bar'>
        <span className='backBtn' onClick={this.gobackFun.bind(this)}></span>
        <span className='close-icon' onClick={this.closePage}></span>
        <span className='nav-top-content'>{ title }</span>
      </div>
    )
  }
}

export default NavTopBar
