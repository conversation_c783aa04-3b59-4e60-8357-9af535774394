import React, { useEffect, useState, useImperative<PERSON><PERSON>le, forwardRef, useRef } from 'react'
import { SearchBar, CheckList } from 'antd-mobile-v5'
import Empty from 'components/Empty'
import { cloneDeep, isEmpty } from 'lodash'
import './index.less'

const SelectPeople = (props, ref) => {
  const searchRef = useRef(null)
  const visible = props.visible
  const title = props.title || '请选择人员'
  const allPeople = props.allPeople || []
  const [selectPeople, setSelectPeople] = useState([])
  const [showPeople, setShowPeople] = useState([])

  // 完成
  const confirmOK = (value) => {
    let selectedPeople = cloneDeep(selectPeople)
    if (!isEmpty(value)) {
      selectedPeople = value
    }
    if (typeof props.onOk === 'function') {
      const result = selectedPeople.length ? allPeople.find(e => e.login_name === selectedPeople[0]) : {}
      props.onOk(result)
    }
  }

  const handleSelect = (value) => {
    setSelectPeople(value)
    if (value.length) {
      confirmOK(value)
    }
  }
  // 搜索框值改变回调函数
  const searchChange = (val) => {
    if (val) {
      const arr = []
      allPeople.length && allPeople.forEach(item => {
        if ((item.real_name.indexOf(val) !== -1 || item.login_name.indexOf(val) !== -1) && arr.length < 30) {
          arr.push(item)
        }
      })
      setShowPeople(arr)
    } else {
      setShowPeople(allPeople.length > 100 ? allPeople.slice(0, 100) : allPeople)
    }
  }

  useEffect(() => {
    if (typeof searchRef?.current?.focus === 'function') {
      searchRef.current.focus()
    }
  }, [visible])

  useEffect(() => {
    const data = props.allPeople || []
    const arr = data.length > 100 ? data.slice(0, 100) : data
    setShowPeople(arr)
  }, [props.allPeople])
  // 取消
  const receptionCancal = () => {
    if (typeof props.onCancal === 'function') {
      props.onCancal()
    }
  }

  useImperativeHandle(ref, () => ({
    onSelectPeople: (selectPeople) => {
      setSelectPeople([props.selectPeople])
    }
  }))

  return <div>
    {
      visible ? <div className='select-people-wrapper'>
        <div className='header'>
          <span onClick={receptionCancal}>取消</span>
          <span>{title}</span>
          <span onClick={confirmOK}>完成</span>
        </div>
        <div className='people-content'>
          <SearchBar
            ref={searchRef}
            className='search'
            placeholder="请输入姓名搜索"
            onChange={(val) => searchChange(val)}
          />
          {
            showPeople.length ? (
              <CheckList
                value={selectPeople}
                className='people-list'
                onChange={handleSelect}
              >
                {
                  showPeople.length && showPeople.map((item, index) => (
                    <CheckList.Item
                      key={index}
                      value={item.login_name}
                    >
                      {`${item.real_name}-${item?.dept[0]?.name}-${item.email}`}
                    </CheckList.Item>
                  ))
                }
              </CheckList>
            ) : <Empty describe='暂无数据' />
          }
        </div>
      </div> : null
    }
  </div>
}

export default forwardRef(SelectPeople)
