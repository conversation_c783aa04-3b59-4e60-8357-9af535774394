.select-people-wrapper {
  background: #ffffff;
  height: 100vh;
  width: 100vw;
  overflow: scroll;
  display: block;
  position: fixed;
  // top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  height: 100%;
  width: 100%;
  z-index: 2;
  animation: receptionAction .2s ease-in-out 1;
  -webkit-animation: receptionAction .2s ease-in-out 1;
  .header{
    width: 750px;
    height: 88px;
    display: flex;
    // justify-content: space-around;
    justify-content: space-between;
    padding: 0px 40px;
    align-items: center;
    font-size: 36px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 500;
    span:nth-child(1) {
      color: #666666;
    }
    span:nth-child(2) {
      color: #000000;
    }
    span:nth-child(3) {
      color: #00C8B7;
    }
  }
  .people-content{
    .search{
      width: 670px;
      background: #F2F2F2;
      border-radius: 10px;
      margin: 16px auto;
      font-size: 30px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
    }
    .people-list {
      // padding: 0px 20px;
      color: #000000;
      // adm-check-list-item-active
      .adm-check-list-item-active, .adm-check-list-item-extra {
        color: #25e6d2;
      }
      a {
        color: #000000;
      }
    }
  }
}
@keyframes receptionAction {
  from { bottom: -100vh; }
  to { bottom: 0px; }
}
@-webkit-keyframes receptionAction {
  from { bottom: -100vh; }
  to { bottom: 0px; }
}
@keyframes receptionActiondown {
  from { bottom: 0; }
  to { bottom: -100vh; }
}
@-webkit-keyframes receptionActiondown {
  from { bottom: 0; }
  to { bottom: -100vh; }
}