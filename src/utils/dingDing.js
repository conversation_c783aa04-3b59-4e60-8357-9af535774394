/***
 * 钉钉相关API
 */
import * as dd from 'dingtalk-jsapi'

// 钉钉扫码
export const dingScanCode = (successFun, errFun) => {
  if (dd.env.platform === 'notInDingTalk') {
    return
  }
  dd.biz.util.scan({
    type: 'all', // type 为 all、qrCode、barCode，默认是all。
    onSuccess: function(data) {
      if (typeof successFun === 'function') {
        successFun(data?.text)
      }
    },
    onFail: function(err) {
      if (typeof successFun === 'function') {
        errFun(err)
      }
    }
  })
}
