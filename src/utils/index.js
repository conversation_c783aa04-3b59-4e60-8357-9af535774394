import './protoExtension'
import srcConfig from 'src/config'

const userAgent = navigator.userAgent
const toString = Object.prototype.toString
const location = window.location

export const noop = _ => _

/**
 * 标志废弃的高阶函数
 * @param {String} fnName 函数名
 * @param {String} readmeUrl 提示地址
 * @example
 * const setAtomParamsWapper = deprecatedWrapper('setAtomParamsWapper', 'https')(() => {})
 * 详见可参考 src/decorator/service-assister.js
 */
export const deprecatedWrapper = (fnName, readmeUrl) => fn => (...args) => {
  console.warn(`[boc.react]: 🚫${fnName || fn.name}已经废弃, 更多用法请参考: ${readmeUrl || ''}`)
  return fn(...args)
}

/**
 * 单例
 * @param {Function} fn 被装饰的函数
 * @return {Function} 代理函数, 接收被装饰的函数一模一样的参数
 */
export const getSingleton = (fn) => {
  let result
  let flag
  return function singletonProxy() {
    if (!flag) {
      flag = true
      return (result = fn.apply(this, arguments))
    } else {
      return result
    }
  }
}

export const getUrlQuery = name => {
  let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
  if (location.href.indexOf('?') < 0) return false
  let search = location.search.substring(1) ? location.href.split('?')[1] : location.href.split('?')[1].replace(/#!\/.+/, '') || ''
  if (!search) return false
  let r = search.replace(/(#|\/)+/ig, '').match(reg)
  if (r != null) {
    // 对编码的字符串进行解码
    let unescapeStr = unescape(decodeURIComponent(r[2]))
    switch (unescapeStr) {
      case 'true':
        return true
      case 'null':
        return null
      case 'false':
        return false
      case 'undefined':
        return undefined
      default:
        return unescapeStr
    }
  } else {
    return null
  }
}

const $common = {
  // isIos: userAgent.match(/iPhone/i) || userAgent.match(/iPad/i) || userAgent.match(/iPod/i),

  // isAndroid: !$common.isIos,

  // isWx: userAgent.match(/MicroMessenger/i),

  // isFirefox: userAgent.match(/Firefox/i),

  // isPc: !['Android', 'iPhone', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod'].some((model) => userAgent.indexOf(model) > -1),

  ua: (function () {
    const regs = {
      // 系统
      // 'ios': /iphone|ipad|ipod/,
      'android': /android/i,

      // 机型
      'iphone': /iphone/i,
      'ipad': /ipad/i,
      'ipod': /ipod/i,

      // 环境
      'weixin': /micromessenger/i,
      'mqq': /QQ\//i,
      'app': /inke/i,
      'alipay': /aliapp/i,
      'weibo': /weibo/i,

      // 浏览器
      'chrome': /chrome\//i
    };

    const ret = {}
    Object.keys(regs).forEach((key) => {
      var reg = regs[key]
      ret[key] = reg.test(userAgent)
    })

    ret.ios = ret.iphone || ret.ipad || ret.ipod;
    ret.mobile = ret.ios || ret.android;
    ret.pc = !ret.mobile;

    ret.chrome = !!window.chrome

    // 经过多次坑验证, 只有在同时有uid 和 sid的时候, 才可以认为是映客环境, 其他的ua等等判断都不靠谱, 虽然有些low, 但是最稳定
    ret.isInke = !!(getUrlQuery('uid') && getUrlQuery('sid'))

    return ret;
  })(),

  /**
   * 客户端版本检查
   * ver <= 客户端当前版本时return true
   */
  checkVersion: (ver) => {
    let cv = $common.getUrlQuery('cv')
    if (!cv) {
      return null
    }
    let thisVersion = cv.replace(/[^\d.]/g, '')
    return ver <= thisVersion
  },

  regs: {
    telephone: /^(0|86|17951)?(13[0-9]|15[012356789]|17[678]|18[0-9]|14[57])[0-9]{8}$/,
    http: /http(|s):\/\//
  },

  /**
   * 设置 iPhoneX viewport 填充模式。
   * @param {"contain"|"cover"} mode
   */
  setViewportFit(mode) {
    const meta = document.querySelector(`meta[name="viewport"]`);
    const content = meta.getAttribute('content');
    const parts = (content || '').replace(/\s/g, '').split(',').map(token => token.split('='));

    let index = -1;

    for (let i = 0; i < parts.length; ++i) {
      if (parts[i][0] === 'viewport-fit') {
        index = i;
        break;
      }
    }

    if (~index) {
      parts[index][1] = mode;
    } else {
      parts.push(['viewport-fit', mode]);
    }

    meta.setAttribute('content', parts.map(token => token.join('=')).join(','));
  },

  getOsVersion: _ => {
    let reg = /(\S+)_(\d+)/
    let os = $common.getUrlQuery('osversion') || false
    if (!os) {
      return false
    }
    let match = reg.exec(os)
    return {
      os: match[1],
      version: match[2]
    }
  },

  /**
   * 参数格式化, 符合url方式
   * @params {Object} {a: '123', age: '18'}
   * @return {String} 'a=123&age=18'
   */
  stringifyParams(params, cb) {
    let name
    let value
    let str = ''

    for (name in params) {
      value = params[name]
      str += name + '=' + (typeof cb === 'function' ? cb(value, name) : value) + '&'
    }

    return str.slice(0, -1)
  },

  /**
  * 将url中? 后面的参数, 变成一个json
  * @return {Object}
  * @example
  * '#hash?a=1&b=3' => {a: 1, b: 3}
  * '?a=1&b=3#hash' => {a: 1, b: 3}
  * '?a=1&b=3#hash?a=2&b=4' => {a: 2, b: 4}
  */
  getUrlParams(sourceStr) {
    // 防止hash值, 影响参数名称
    let search
    if (sourceStr) {
      // 只取最后一个?号后面的参数
      search = sourceStr.indexOf('?') > -1 ? sourceStr.split('?').slice(-1).toString() : sourceStr
    } else {
      // 链接中的最后一个
      search = location.search.substr(1);
      let hashSearch = location.hash.split('?')[1] || '';
      search = search ? `${search}&${hashSearch}` : hashSearch;
    }

    // 如果没有, 则返回空对象
    if (!search) return {}

    let searchArr = decodeURIComponent(search).split('&')

    let urlParams = {}

    searchArr.forEach((str, index) => {
      let paramArr = str.split('=')
      // 过滤空字符串
      if (!paramArr[0]) return false;
      // 后面重复的参数覆盖前面的参数
      urlParams[paramArr[0]] = unescape(paramArr[1])
    })
    return urlParams
  },

  /**
   * 根据日期获取星座
   * @param {String} date
   */
  getStarSigns(date) {
    const getAstro = (m, d) => {
      return '魔羯水瓶双鱼牡羊金牛双子巨蟹狮子处女天秤天蝎射手魔羯'.substr(m * 2 - (d < '102223444433'.charAt(m - 1) - -19) * 2, 2);
    }
    let d = new Date(date.replace(/-/g, '/'))
    return getAstro(d.getMonth() + 1, d.getDate()) + '座'
  },

  /**
   * 得到url中某个参数
   */
  getUrlQuery,

  /**
   * 图片缩放
   * @param param
   * @return {string}
   */
  scaleImg(param) {
    var opt = {
      url: '',
      w: '100',
      h: '100',
      s: 80
    };
    if (!param || !param.url) {
      return;
    }
    opt = Object.assign({}, opt, param || {});
    var base = location.protocol + '//imagescale.inke.cn/imageproxy2/dimgm/scaleImage';
    return (
      base +
      '?url=' +
      encodeURIComponent(opt.url) +
      '&w=' +
      opt.w +
      '&h=' +
      opt.h +
      '&s=' +
      (opt.s || 80)
    );
  },
  /**
   * 活动链接内部跳转
   */
  activityUrlWithoutSearch(url) {
    let _url = url
    let _locationParams = $common.getUrlParams()
    let _urlParams = $common.getUrlParams(_url)
    if (_urlParams.inkewid) { // 跳转 url 有 inkewid，替换 location 的 inkewid
      let _urlObj = {
        ..._locationParams,
        ..._urlParams,
        from: _locationParams.from || _urlParams.from
      }
      _url = `${_url.split('?')[0]}?${$common.stringifyParams(_urlObj)}`
    } else {
      if (_locationParams.inkewid) { // location 有 inkewid，url 无 inkewid，删除 location 的 inkewid
        delete _locationParams.inkewid
        delete _locationParams.inkewname
        delete _locationParams.inkewtype
        _url = _url.indexOf('?') >= 0 ? `${_url}&${$common.stringifyParams(_locationParams)}` : `${_url}?${$common.stringifyParams(_locationParams)}`
      } else {
        _url = _url.indexOf('?') >= 0 ? `${_url}&${location.search.replace('?', '')}` : `${_url}${location.search}`
      }
    }
    let a = document.createElement('a')
    a.href = _url
    a.click()
  },

  /**
   * app内直接跳转到某个页面
   */
  innerAppSkipUrl(url) {
    let a = document.createElement('a')
    a.href = url
    a.click()
  },

  /**
   * 判断对象是否为空
   * @return {Boolean} 是否是空对象
   */
  isEmptyObject(obj) {
    let key

    for (key in obj) {
      return false
    }

    return true
  },

  isObject(obj) {
    return toString.call(obj) === '[object Object]'
  },

  /**
   * 拷贝, 支持深拷贝, 支持多个参数
   * 第一个参数如果为 boolean类型且为true, 做深拷贝
   * @example
   * 浅拷贝 common.copy({name: 'libaoxu'}, {age: 18}) => { name: 'libaoxu', age: 18 }
   * 深拷贝 common.copy(true, {name: 'libaoxu', age: 18, obj: {sex: '1', love: 'bei'}}, {name: 'deep', obj: {sex: '2'}}) => { name: 'deep', age: 18, obj: { sex: 2, love: 'bei' } }
   */
  copy() {
    let target = arguments[0] || {}
    let i = 1
    let length = arguments.length
    let deep = false

    if (typeof target === 'boolean') {
      deep = target
      target = arguments[1] || {}
      i++
    }

    if (typeof target !== 'object' && typeof target !== 'function') {
      target = {}
    }

    for (; i < length; i++) {
      let options
      if ((options = arguments[i]) != null) {
        for (let prop in options) {
          let src = target[prop]
          let copy = options[prop]

          if (target === copy) continue

          let copyIsArray

          if ((deep && copy) ? $common.isObject(copy) : (copyIsArray = Array.isArray(copy))) {
            let clone
            if (copyIsArray) {
              copyIsArray = false
              clone = src && Array.isArray(src) ? src : []
            } else {
              clone = src && $common.isObject(copy) ? src : {}
            }

            target[prop] = $common.copy(deep, clone, copy)
          } else if (copy != null) {
            target[prop] = copy
          }
        }
      }
    }

    return target
  },

  /**
   * 节流函数
   * @param {Function} func 回调函数
   * @param {Number} wait 等待时间
   * @param {Object} options 配置参数
   * @property options.leading false: 如果你想禁用第一次首先执行的
   * @property options.trailing false: 你想禁用最后一次执行的话
   */
  throttle(func, wait, options = {}) {
    let timeout
    let context
    let args
    let result
    let previous = 0
    if (!options) options = {}

    const later = function () {
      previous = options.leading === false ? 0 : Date.now()
      timeout = null
      result = func.apply(context, args)
      if (!timeout) context = args = null
    }

    const throttled = function () {
      let now = Date.now()
      if (!previous && options.leading === false) previous = now
      let remaining = wait - (now - previous)
      // console.log('remaining: ', remaining, 'now: ', now, 'previous: ', previous, remaining > wait)
      context = this
      args = arguments
      // remaining > wait 防止用户修改系统时间
      if (remaining <= 0 || remaining > wait) {
        if (timeout) {
          // console.log('clear timeout')
          clearTimeout(timeout)
          timeout = null
        }
        // console.log('remaining <=0 || remaining > wait')
        // 进来之后 previous 才被赋值, 保证第一次执行成功
        previous = now
        result = func.apply(context, args)
        if (!timeout) context = args = null
      } else if (!timeout && options.trailing !== false) { // !timeout, 保证上一次later执行完的 标识
        // console.log('!timeout: ', timeout)
        timeout = setTimeout(later, remaining)
      }
      return result
    }

    throttled.cancel = function () {
      clearTimeout(timeout)
      previous = 0
      timeout = context = args = null
    }

    return throttled
  },

  noop() {
  },

  isDef: v => (v !== undefined),

  createPromise() {
    let _resolve
    let _reject
    let promise = new Promise((resolve, reject) => {
      _resolve = resolve
      _reject = reject
    })

    return {
      promise,
      resolve: _resolve,
      reject: _reject
    }
  },
  // 注意: 因为会给imgScale用, 必须要有protocol, 否则报错,
  getImgUrlAdapter(url, defaultUrl = `${window.location.protocol}//img2.inke.cn/MTUyODQ0Nzk2NTg5MyM0ODEjanBn.jpg`) {
    const httpReg = this.regs.http;
    if (url) {
      if (httpReg.test(url)) {
        return url.replace(httpReg, `${window.location.protocol}//`)
      }

      return `${window.location.protocol}//img2.inke.cn/${url}`
    }
    return url || defaultUrl
  },

  formatTime: function (seconds, guide = seconds) {
    seconds = seconds < 0 ? 0 : seconds;
    let s = Math.floor(seconds % 60);
    let m = Math.floor(seconds / 60 % 60);
    let h = Math.floor(seconds / 3600);
    const gm = Math.floor(guide / 60 % 60);
    const gh = Math.floor(guide / 3600);

    // handle invalid times
    if (isNaN(seconds) || seconds === Infinity) {
      // '-' is false for all relational operators (e.g. <, >=) so this setting
      // will add the minimum number of fields specified by the guide
      h = m = s = '-';
    }

    // Check if we need to show hours
    h = (h > 0 || gh > 0) ? h + ':' : '';

    // If hours are showing, we may need to add a leading zero.
    // Always show at least one digit of minutes.
    m = (((h || gm >= 10) && m < 10) ? '0' + m : m) + ':';

    // Check if leading zero is need for seconds
    s = (s < 10) ? '0' + s : s;

    return h + m + s;
  },

  completImgUrlWithHost(url, imgHost = 'https://img2.inke.cn/') {
    // http开头的, 变为https
    if (url && typeof url === 'string') {
      if (url.indexOf('http://') > -1) {
        return url.replace('http://', 'https://')
      } else {
        // 路径结尾的,
        return `${imgHost}${url}`
      }
    } else {
      // return require('src/assets/images/common/default-portrait.png')
      return 'https://'
    }
  },

  getOpenInkeUrl(url) {
    const scheme = 'inke://pname=web&url='
    return scheme + encodeURIComponent(url)
  },

  getOpenInkeUrlQrCode(key = 'local') {
    if (srcConfig.IS_DEV) {
      const keys = {
        local: location.host,
        818: 'https://testboc.inke.cn:818',
        828: 'https://testboc.inke.cn:828',
        838: 'https://testboc.inke.cn:838',
        848: 'https://testboc.inke.cn:848',
        858: 'https://testboc.inke.cn:858',
        gray: 'https://betaboc.inke.cn',
        online: 'https://boc.inke.cn'
      }
      const pathname = location.pathname
      const search = location.search
      const scheme = 'inke://pname=web&url='
      let url = scheme + encodeURIComponent((keys[key] || keys['local']) + pathname + search)
      if (window.QRCode) {
        window.QRCode.toDataURL(url).then(base64 => {
          console.log(base64)
          console.log('%c ', `padding:100px 100px; background:url(${base64}) no-repeat;`)
        })
      } else {
        console.log(url)
      }
    }
  },
  /**
   * 保留小数
   * @param fractionDigits
   */
  toFixed(number, fractionDigits = 0) {
    if (Number.isNaN(parseFloat(number))) {
      return 0;
    }
    return Number(parseFloat(number).toFixed(fractionDigits));
  }
}

export default $common
