import Confirm from 'components/confirm'
export const AppendixView = (url, name) => {
  // url = 'https://family-upload.inkept.cn/api/v1/download/OW5jQ7nWLn9L9QIHOGyAng=='
  // name = '附件二：2021年推广服务合同之返点补充协议-20210510.doc'
  if (!url) {
    Confirm.ToastMessage('附件URL地址为空')
    return
  }
  const suffix = name.substring(name.lastIndexOf('.') + 1)
  if (url.indexOf(`.${suffix}`) === -1) {
    url = `${url}.${suffix}?suffix=${suffix}`
  }
  let currentUrl = window.encodeURIComponent(window.btoa(url))
  let exportUrl
  const afterStr = url.substring(url.lastIndexOf('.') + 1)
  if (afterStr === 'pdf') {
    exportUrl = url
  } else {
    exportUrl = 'https://oa.inkept.cn/preview/onlinePreview?watermarkTxt=&url=' + currentUrl
  }
  var currentNode = document.createElement('a')
  currentNode.setAttribute('href', exportUrl)
  currentNode.setAttribute('target', '_blank')
  currentNode.click()
}
