@import '../variable.less';
@import 'motion';
@import 'fade';
@import 'move';
@import 'other';
@import 'slide';
@import 'swing';
@import 'zoom';

// For common/openAnimation
.ik-motion-collapse-legacy {
  overflow: hidden;
  &-active {
    transition: height 0.15s @ease-in-out, opacity 0.15s @ease-in-out !important;
  }
}

.ik-motion-collapse {
  overflow: hidden;
  transition: height 0.15s @ease-in-out, opacity 0.15s @ease-in-out !important;
}
