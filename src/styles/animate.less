/*
  动画
  统一命名空间 @ani-
  <AUTHOR> 2017-05-24
*/

@keyframes tada {
  0% {
    transform: scale3d(1, 1, 1);
  }
  5%, 10% {
    transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
  }
  15%, 25%, 35%, 45% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  20%, 30%, 40% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  50%, 100% {
    transform: scale3d(1, 1, 1);
  }
}
// animation: tada 2.4s linear 0s infinite;

@keyframes bounce {
  0%, 14%, 37%, 56%, 70% {
    animation-timing-function: cubic-bezier(.220, .600, .400, 1.000);
    transform: translate3d(0, 0, 0);
  }
  28%, 30% {
    animation-timing-function: cubic-bezier(.800, .050, .800, .060);
    transform: translate3d(0, -14px, 0);
  }
  49% {
    animation-timing-function: cubic-bezier(.800, .050, .800, .060);
    transform: translate3d(0, -10px, 0);
  }
  63% {
    transform: translate3d(0, -4px, 0);
  }
}
// animation: bounce 2s linear 0s infinite;

@keyframes teetertotter {
  0% {
    transform:translateY(0);
  }
  50% {
    transform:translateY(10px);
  }
  100% {
    transform:translateY(0);
  }
}
// animation: teetertotter 1.2s linear 0s infinite;

@keyframes rubberBand {
  0% {
    transform: scale3d(1, 1, 1);
  }
  30% {
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    transform: scale3d(.95, 1.05, 1);
  }
  75% {
    transform: scale3d(1.05, .95, 1);
  }
  100% {
    transform: scale3d(1, 1, 1);
  }
}

@keyframes jello {
  0%, 11.1%, 100% {
    transform: none;
  }
  22.2% {
    transform: skewX(-12.5deg) skewY(-12.5deg);
  }
  33.3% {
    transform: skewX(6.25deg) skewY(6.25deg);
  }
  44.4% {
    transform: skewX(-3.125deg) skewY(-3.125deg);
  }
  55.5% {
    transform: skewX(1.5625deg) skewY(1.5625deg);
  }
  66.6% {
    transform: skewX(-0.78125deg) skewY(-0.78125deg);
  }
  77.7% {
    transform: skewX(0.390625deg) skewY(0.390625deg);
  }
  88.8% {
    transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
  }
}

@keyframes flip {
  0% {
    transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    animation-timing-function: ease-out;
  }
  40% {
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    animation-timing-function: ease-out;
  }
  50% {
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    animation-timing-function: ease-in;
  }
  80% {
    transform: perspective(400px) scale3d(.95, .95, .95);
    animation-timing-function: ease-in;
  }
  100% {
    transform: perspective(400px);
    animation-timing-function: ease-in;
  }
}

@keyframes tranX1{
  0%{
    transform: translateX(0);
  }
  100%{
    transform: translateX(400px);
  }
}

@keyframes tranX2{
  0%{
    transform: translateX(0);
  }
  100%{
    transform: translateX(-400px);
  }
}

@keyframes swing-linear {
  0% {
    transform: rotate3d(0, 0, 1, 0deg)
  }
  30% {
    transform: rotate3d(0, 0, 1, 10deg)
  }
  50% {
    transform: rotate3d(0, 0, 1, 0deg)
  }
  70% {
    transform: rotate3d(0, 0, 1, -10deg)
  }
  100% {
    transform: rotate3d(0, 0, 1, 0deg)
  }
}

// @desc:http://www.html5tricks.com/demo/css3-animate-css/index.html
@keyframes flipOutX {
  0% {
      -webkit-transform: perspective(400px);
      -ms-transform: perspective(400px);
      transform: perspective(400px)
  }

  30% {
      -webkit-transform: perspective(400px) rotate3d(1,0,0,-20deg);
      -ms-transform: perspective(400px) rotate3d(1,0,0,-20deg);
      transform: perspective(400px) rotate3d(1,0,0,-20deg);
      opacity: 1
  }

  100% {
      -webkit-transform: perspective(400px) rotate3d(1,0,0,90deg);
      -ms-transform: perspective(400px) rotate3d(1,0,0,90deg);
      transform: perspective(400px) rotate3d(1,0,0,90deg);
      opacity: 0
  }
}

@keyframes bounceInDown {
  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -3000px, 0);
    transform: translate3d(0, -3000px, 0);
  }

  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, 25px, 0);
    transform: translate3d(0, 25px, 0);
  }

  75% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }

  90% {
    -webkit-transform: translate3d(0, 5px, 0);
    transform: translate3d(0, 5px, 0);
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }

  50% {
    opacity: 1;
  }
}
