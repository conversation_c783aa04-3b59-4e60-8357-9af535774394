/*
  常用样式工具方法
  统一命名空间 .com-
  <AUTHOR> 2017-05-22
*/

@com-color: black;
.com-triangle-down (@color, @size: 6) {
  width: 0;
  height: 0;
  border-top: @size * 1px solid @color;
  border-left: @size * 1px solid transparent;
  border-right: @size * 1px solid transparent;
  border-bottom: 0;
  position: absolute;
  top: 50%;
  margin-top: -(@size / 2) * 1px;
}

.com-triangle-up (@color, @size: 6) {
  width: 0;
  height: 0;
  border-bottom: @size * 1px solid @color;
  border-left: @size * 1px solid transparent;
  border-right: @size * 1px solid transparent;
  border-top: 0;
  position: absolute;
  top: 50%;
  margin-top: -(@size / 2) * 1px;
}

.com-gradient (@start: top, @from, @to){
  background-image: -webkit-gradient(linear, @start, from(@from), to(@to));
  background-image: -moz-linear-gradient(@start, @from, @to);
  background-image: -o-linear-gradient(@start, @from, @to);
  background-image: -webkit-linear-gradient(@start, @from, @to);
}

.com-sprite-bg (@sprite-path, @sprite-width, @sprite-height, @x, @y) {
  background: url(@sprite-path) no-repeat @x @y;
  background-size: @sprite-width @sprite-height;
}

.com-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.background (@width, @height, @url, @bg-color: transparent) {
  width: @width;
  height: @height;
  background: url(@url) no-repeat @bg-color;
  background-size: 100%;
}

.placeholder-color (@color) {
  &::-webkit-input-placeholder {
    color: @color;
  }
  &::-moz-placeholder {
    color: @color;
  }
  &:-moz-placeholder {
    color: @color;
  }
  &::-ms-input-placeholder {
    color: @color;
  }
}
