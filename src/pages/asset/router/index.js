import React, { Suspense, lazy } from 'react'
import { <PERSON>, Switch, Hash<PERSON><PERSON>er, B<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import LoadingView from 'components/_loading'

const Asset = lazy(() => import('../views'))
const SelfInventory = lazy(() => import('../views/components/SelfInventory'))
const AdministrativeInventory = lazy(() => import('../views/components/AdministrativeInventory'))
const AssetInventoryList = lazy(() => import('../views/components/AssetInventoryList'))
const SelfInventoryDetail = lazy(() => import('../views/components/SelfInventoryDetail'))
const NoInventory = lazy(() => import('../views/components/NoInventory'))
const MyAssetList = lazy(() => import('../views/components/MyAssetList'))

const getRouter = _ => (
  <HashRouter>
    <Suspense fallback={<LoadingView />}>
      {/* 异步引入 */}
      <Route exact path="/" render={props => <Asset {...props}/>} />
      <Route exact path="/inventory/self" render={props => <SelfInventory {...props}/>} />
      <Route exact path="/inventory/administrative/:plan_id" render={props => <AdministrativeInventory {...props}/>} />
      <Route exact path="/inventory/self/list/:id" render={props => <AssetInventoryList {...props}/>} />
      {/* <Route exact path="/inventory/self/detail/:inventoryId/:id" render={props => <SelfInventoryDetail {...props}/>} /> */}
      <Route exact path="/inventory/self/detail" render={props => <SelfInventoryDetail {...props}/>} />
      <Route exact path="/inventory/self/noInventory/:inventoryId/:id" render={props => <NoInventory {...props}/>} />
      <Route exact path="/asset/list/:type" render={props => <MyAssetList {...props}/>} />
    </Suspense>
  </HashRouter>
)

export default getRouter
