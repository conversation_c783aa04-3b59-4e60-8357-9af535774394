/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-4-20
 * @Description: 固定资产管理
 */
import React, { PureComponent } from 'react'
import setTitle from 'components/setBarTitle'
import { SELF_INVENTORY, ADMINISTRATIVE_INVENTORY } from './constants'
import API from './apis'
import _ from 'lodash'
import './index.less'

class AssetsManagement extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      assetStatus: [{
        type: 'all_cnt',
        name: '全部资产',
        value: 0
      }, {
        type: 'use_cnt',
        name: '使用中',
        value: 0
      }, {
        type: 'borrow_cnt',
        name: '借用中',
        value: 0
      }],
      assetManagement: [{
        type: 'have_staff_check_plan',
        isPermission: false,
        name: '自助盘点',
        icon: 'https://img.ikstatic.cn/MTY1MDUwOTk0NDE1NyM5MzgjcG5n.png',
        url: '/inventory/self',
        search: `?type=${SELF_INVENTORY}`
      }, {
        type: 'have_admin_check_plan',
        isPermission: false,
        name: '行政盘点',
        icon: 'https://img.ikstatic.cn/MTY1MDUwODQ5OTU1NyMgNDIjcG5n.png',
        // url: '/inventory/administrative'
        url: '/inventory/self',
        search: `?type=${ADMINISTRATIVE_INVENTORY}`
      }],
    }
    this.userInfo = {}
  }

  componentDidMount() {
    setTitle('资产中心')
    this.userInfo = JSON.parse(window.localStorage.getItem('user') || '{}')
    this.getAssetHomeInfo()
  }

  handleAssetOperation(data) {
    this.props.history.push({
      pathname: data.url,
      search: data.search
    })
  }

  handleJumpMyAsset(data) {
    this.props.history.push('/asset/list/' + data.type)
  }

  getAssetHomeInfo() {
    API.getAssetHomeInfo().then(res => {
      const { assetStatus, assetManagement } = this.state
      const data = res.data || {}
      if (!_.isEmpty(data)) {
        const assetStatusResult = _.cloneDeep(assetStatus)
        const assetManagementResult = _.cloneDeep(assetManagement)
        assetStatusResult.forEach(item => {
          if (data.hasOwnProperty(item.type)) {
            item.value = data[item.type] || 0
          }
        })
        assetManagementResult.forEach(item => {
          if (data.hasOwnProperty(item.type)) {
            item.isPermission = data[item.type] || false
          }
        })
        this.setState({
          assetStatus: assetStatusResult,
          assetManagement: assetManagementResult
        })
      }
    }).catch(err => {
      console.log(err)
    })
  }

  render () {
    const { assetStatus, assetManagement } = this.state
    return (
      <div className='assets-management'>
        <div className='top-asset-visual'>
          <div className='top-background-wrapper'>
            <div className='show-more-info'>
              <div>Hi，{this.userInfo?.realname}</div>
              {/* <div>
                <img src='https://img.ikstatic.cn/MTY1MDQ1MDAxNDgwMyMxOTcjcG5n.png' alt='' />
              </div> */}
            </div>
          </div>
          <div className='my-asset-wapper'>
            <h2 className='asset-title'>我的资产</h2>
            <div className='asset-status-wrapper'>
              {
                assetStatus.map((item, i) => (
                  <div
                    key={i}
                    className='asset-status-content'
                    onClick={this.handleJumpMyAsset.bind(this, item)}
                  >
                    <div className='status-value'>{item.value}</div>
                    <div>{item.name}</div>
                  </div>
                ))
              }
            </div>
          </div>
        </div>
        <div className='asset-list-wapper'>
          <h2 className='asset-title'>资产管理</h2>
          <div className='asset-type-wrapper'>
            {
              assetManagement.map((item, i) => (
                <div
                  key={i}
                  className={item?.isPermission ? 'asset-type-item' : 'hidden'}
                  onClick={this.handleAssetOperation.bind(this, item)}
                >
                  <img src={item.icon} className='type-icon' alt='' />
                  <span className='type-title'>{item.name}</span>
                </div>
              ))
            }
          </div>
        </div>
      </div>
    )
  }
}

export default AssetsManagement
