.assets-management {
  background: #F0F5F5;
  min-height: 100vh;
  .top-asset-visual {
    position: relative;
    height: 480px;
    .top-background-wrapper {
      background: #00d8c9;
      height: 300px;
    }
    .show-more-info {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      height: 120px;
      padding: 0px 20px;
      color: #FFFFFF;
      font-size: 32px;
      font-family: PingFangSC-Medium, PingFang SC;
    }
    .asset-status-wrapper {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      padding: 20px 0px !important;
    }
    .asset-status-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      max-width: 30%;
      .status-value {
        margin-bottom: 5px;
        color: #00d8c9;
        font-size: 35px;
        font-weight: 500;
        font-family: PingFangSC-Medium, PingFang SC;
        width: 100%;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .my-asset-wapper {
      position: absolute;
      height: 300px;
      top: 120px;
      left: 0px;
      right: 0px;
      overflow: hidden;
      background: #FFFFFF;
      border-radius: 15px;
      margin: 0px 20px;
      padding: 30px;
      box-shadow: 1px 5px 13px 1px #dfe3e3;
      .asset-title {
        border-bottom: 2px solid #F2F2F2;
        padding-bottom: 20px;
        font-size: 36px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #000000;
      }
    }
  }
  .asset-list-wapper {
    background: #FFFFFF;
    border-radius: 15px;
    margin: 0px 20px;
    padding: 30px;
    box-shadow: 1px 5px 13px 1px #dfe3e3;
    .asset-title {
      border-bottom: 2px solid #F2F2F2;
      padding-bottom: 20px;
      font-size: 36px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #000000;
    }
    .asset-type-wrapper {
      display: flex;
      flex-flow: wrap;
      margin-top: 38px;
      .asset-type-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 25%;
        height: 150px;
      }
      .type-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 10px;
      }
      .type-title {
        line-height: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
  }
}
.hidden {
  display: none;
}
