/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-4-26
*/

import { getRequestsByRoot } from 'axios-service'
import srcConfig from 'src/config/apiRoots'

const { get, post } = getRequestsByRoot({ root: srcConfig.assetRoot })
class Apis {
  /**
   * 员工盘点-资产首页
   * https://yapi.inkept.cn/project/2279/interface/api/22289
   */
  getAssetHomeInfo = get('asset/api/v1/check/staff/home', {}, { autoLoading: true })

  /**
   * 员工盘点-盘点单列表
   * https://yapi.inkept.cn/project/2279/interface/api/22291
   */
  getSelfInventoryList = get('asset/api/v1/check/staff/plan-list', {}, { autoLoading: true })

  /**
   * 员工盘点-盘点资产列表
   * https://yapi.inkept.cn/project/2279/interface/api/22292
   */
  getSelfInventoryAssetsList = get('asset/api/v1/check/staff/plan-detail/list', {}, { autoLoading: true })

  /**
   * 盘点计划-资产盘点详情
   * https://yapi.inkept.cn/project/2279/interface/api/22292
   */
   getInventoryAssetsDetail = get('asset/api/v1/check/plan/check-detail', {}, { autoLoading: true })

   /**
   * 盘点计划-执行盘点提交
   * https://yapi.inkept.cn/project/2279/interface/api/22294
   */
   submitInventory = post('asset/api/v1/check/staff/plan/check-result/update', {}, { autoLoading: true })

  /**
   * 我的资产-列表
   * https://yapi.inkept.cn/project/2279/interface/api/22300
   */
  getMyAssetList = get('asset/api/v1/check/staff/asset/list', {}, { autoLoading: true })

  /**
   * 盘点计划-行政执行盘点提交
   * https://yapi.inkept.cn/project/2279/interface/api/22530
   */
  adminSumitInventory = post('asset/api/v1/check/staff/plan/admin-check-result/update', {}, { autoLoading: true })
}

export default new Apis()
