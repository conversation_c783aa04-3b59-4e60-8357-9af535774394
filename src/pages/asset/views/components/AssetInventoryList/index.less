.inventory-assset-list {
  background: #F0F5F5;
  min-height: 100vh;
  .inventory-assset-list-wrapper {
    background: #FFFFFF;
    border-radius: 15px;
    margin: 20px;
    box-shadow: 1px 5px 13px 1px #dfe3e3;
  }
  .top-wrapper {
    min-height: 80px;
    border-bottom: 2px solid #F2F2F2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 20px;
    .top-title {
      flex: 1;
      font-size: 30px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      margin-right: 30px;
    }
    .perform-inventory {
      // width: 138px;
      color: #00d8c9;
      &::after {
        content: '>';
        margin-left: 6px;
      }
    }
  }
  .attribute-list {
    padding: 20px;
    .every-type-content {
      margin: 20px 0px;
      .every-type-label {
        display: inline-block;
        font-family: PingFangSC-Medium, PingFang SC;
        // width: 130px;
        // font-size: 32px;
        // min-width: max-content;
        // text-align: justify;
        // text-justify: inter-ideograph;
        // text-align-last: justify;
      }
    }
    .transfer-type {
      margin-left: 10px;
    }
  }
  .scan-btn {
    display: flex;
    align-items: center;
    margin: 0 auto;
    --background-color: #00d8c9;
    --border-color: #00d8c9;
    .scan-code-icon {
      width: 50px;
      height: 50px;
      margin-right: 10px;
    }
  }
  .hide {
    display: none;
  }
}