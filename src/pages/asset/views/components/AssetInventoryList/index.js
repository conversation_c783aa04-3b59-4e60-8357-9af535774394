/**
 * 盘点资产列表
 */
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { NoticeBar, Tag, Tabs, Toast } from 'antd-mobile-v5'
import setTitle from 'components/setBarTitle'
import Empty from 'components/Empty'
import ScanButton from 'components/ScanButton'
import { useHistory } from 'react-router-dom'
import {
  TOP_NOTICE_BAR,
  SELF_INVENTORY_ASSET_LIST,
  getAssetsBaseInfos,
  inventoryResultStatus,
  inventoryResultStatusColor,
  DEFAULT_EMPTY_TIP,
  inventoryListTabs,
  ALL_LIST,
  NO_INVENTORY_LIST,
  YES_INVENTORY_LIST
} from '../../constants'
import { dingScanCode } from 'utils/dingDing'
// import * as dd from 'dingtalk-jsapi'
import './index.less'
import _ from 'lodash'
import API from '../../apis'

const AssetInventoryList = (props) => {
  const inventoryId = props.match.params.id
  const history = useHistory()
  const [inventoryList, setInventoryList] = useState(inventoryListTabs)

  // 获取盘点资产列表
  const getSelfInventoryAssetsList = (id) => {
    API.getSelfInventoryAssetsList({ id: Number(id) }).then(res => {
      const infoList = getAssetsBaseInfos(SELF_INVENTORY_ASSET_LIST)
      const data = _.cloneDeep(res.data?.list || [])
      const numInfo = res.data?.staff_check_stat_info || {}
      // setIsEmptyStatus(!data.length)
      if (data.length) {
        const resultArr = []
        data.forEach(item => {
          const tempObj = {
            id: item.id,
            name: item.asset_code,
            code: item.asset_code,
            assetStatus: item?.check_result ?? -1,
            is_transfer: item.is_transfer
          }
          const itemArr = _.cloneDeep(infoList)
          itemArr.forEach(ele => {
            ele.value = (item[ele.key] || item[ele.key] === 0) ? item[ele.key] : '-'
          })
          resultArr.push({
            ...tempObj,
            asset_info: itemArr
          })
        })
        const resultList = inventoryList.map(item => {
          const list = handleStatusList(item.key, resultArr)
          return {
            ...item,
            list: list,
            isEmptyList: !list.length,
            num: numInfo[item.interfaceField]
          }
        })
        setInventoryList(resultList)
      }
    }).catch(err => {
      console.log(err)
    })
  }

  const handleStatusList = (key, list = []) => {
    switch (key) {
      case ALL_LIST:
        return list
      case NO_INVENTORY_LIST:
        return list.filter(item => item.assetStatus === -1)
      case YES_INVENTORY_LIST:
        return list.filter(item => item.assetStatus === 1)
      default:
        return []
    }
  }

  // 跳转到盘点详情，id:条目ID
  const handleJumpDetail = useCallback(
    (code) => {
      history.push({
        pathname: `/inventory/self/detail`,
        search: `?inventoryId=${inventoryId}&assetCode=${code}`
      })
    },
    [history, inventoryId]
  )

  const handleSancode = useCallback(() => {
    // if (dd.env.platform === 'notInDingTalk') {
    //   // handleJumpDetail('0000004076')
    //   handleJumpDetail('00000087678866')
    // }
    dingScanCode(code => {
      handleJumpDetail(code)
    }, error => {
      if (!error?.errorMessage) return
      Toast.show({
        icon: 'fail',
        content: error.errorMessage,
      })
    })
  }, [handleJumpDetail])

  const renderValue = (item, parentEle) => {
    if (item.key === 'check_result') {
      return (
        <Tag color={inventoryResultStatusColor[item.value]}>
          {(item.value || item.value === 0) ? inventoryResultStatus[item.value] : '-'}
        </Tag>
      )
    } else if (item.key === 'receive_realname') { // 使用人
      return (<span>
        {item.value || '-'}
        {parentEle.is_transfer ? <Tag color='#68d800' className='transfer-type'>转发</Tag> : null}
      </span>)
    } else {
      return <span>{(item.value || item.value === 0) ? item.value : '-'}</span>
    }
  }

  const rendTabTitle = (objData) => {
    return <span>{`${objData.title}(${objData.num})`}</span>
  }

  const renderTabItem = useMemo(
    () => (
      inventoryList.map(item => (
        <Tabs.Tab title={rendTabTitle(item)} key={item.key}>
          {
            <ScanButton
              onClick={handleSancode}
            />
          }
          {
            !item.isEmptyList ? (
              item.list.map((element, index) => (
                <div
                  className='inventory-assset-list-wrapper'
                  key={index}
                  // onClick={() => handleJumpDetail(element.id)}
                >
                  <div className='top-wrapper'>
                    <div className='top-title'>{element.name}</div>
                    {/* <div className='perform-inventory' onClick={() => handleJumpDetail(element.id)}>
                      {
                        element?.assetStatus > 0 ? '查看详情' : '执行盘点'
                      }
                    </div> */}
                    <div
                      className={element?.assetStatus > 0 ? 'perform-inventory' : 'hide'}
                      onClick={() => handleJumpDetail(element.code)}
                    >
                        查看详情
                    </div>
                  </div>
                  <div className='attribute-list'>
                    {
                      !_.isEmpty(element.asset_info) && element.asset_info.map((item, i) => (
                        <div key={i} className='every-type-content'>
                          <span className='every-type-label'>{item.label}</span>
                          <span>：</span>
                          {
                            renderValue(item, element)
                          }
                        </div>
                      ))
                    }
                  </div>
                </div>
              ))
            ) : (
              <Empty describe={DEFAULT_EMPTY_TIP} />
            )
          }
        </Tabs.Tab>
      ))
    ),
    [inventoryList, handleSancode, handleJumpDetail]
  )

  useEffect(() => {
    setTitle('盘点资产列表')
    getSelfInventoryAssetsList(inventoryId)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [inventoryId])

  return <div className='inventory-assset-list'>
    <NoticeBar content={TOP_NOTICE_BAR} color='alert' />
    {
      useMemo(
        () => (
          <Tabs
            activeLineMode='full'
          >
            {
              renderTabItem
            }
          </Tabs>
        ),
        [renderTabItem]
      )
    }
  </div>
}

export default AssetInventoryList
