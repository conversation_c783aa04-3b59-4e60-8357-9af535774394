@warnColor: red;
.self-inventory-detail {
  background: #F0F5F5;
  min-height: 100vh;
  padding: 20px;
  .asset-in-me {
    background: #ffffff;
    border-radius: 15px;
    margin: 20px 0px;
    padding: 20px;
    box-shadow: 1px 5px 13px 1px #dfe3e3;
    .result-title {
      font-size: 32px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      margin-bottom: 30px;
    }
  }
  .asset-case-radio {
    color: @warnColor;;
  }
  .scan-code {
    --background-color: #00d8c9;
    --border-color: #00d8c9;
  }
  .no-inventory-prompt {
    color: @warnColor;;
  }
  .inventory-err-msg {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: @warnColor;
    position: absolute;
    top: 20%;
    left: 50%;
    transform: translate(-50%,-50%);
    font-size: 48px;
    .err-msg-content {
      margin-top: 15px;
    }
  }
}