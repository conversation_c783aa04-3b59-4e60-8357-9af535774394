/**
 * 盘点详情(自助盘点)
 */
import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react'
import { Radio, Space, Button, Toast } from 'antd-mobile-v5'
import { ExclamationShieldOutline } from 'antd-mobile-icons'
import setTitle from 'components/setBarTitle'
import AssetDetail from '../AssetDetail'
import AssetPerfect from '../AssetPerfect'
import { useHistory } from 'react-router-dom'
import { SELF_INVENTORY, getAssetsBaseInfos, IMGAGE_PROMPT } from '../../constants'
import $utils from 'utils'
import API from '../../apis'
import './index.less'
import _ from 'lodash'

const SelfInventoryDetail = (props) => {
  const userInfo = JSON.parse(window.localStorage.getItem('user') || '{}')
  const history = useHistory()

  const assetCode = useRef($utils.getUrlQuery('assetCode'))
  const inventoryId = useRef($utils.getUrlQuery('inventoryId'))
  const id = useRef('')
  const [inventoryShowData, setInventoryShowData] = useState([])
  const [isInMe, setIsInMe] = useState(false) // 资产是否在我手上
  // const [assetCode, setAssetCode] = useState('') // 资产编码
  const [assetStatus, setAssetStatus] = useState(undefined) // 资产状态
  const [note, setNote] = useState('') // 补充备注信息
  const [imageList, setImageList] = useState([]) // 补充图片信息
  const [inventoryFlag, setInventory] = useState(null) // 盘点标识
  const [inventoryErrMsg, setInventoryErrMsg] = useState('') // 扫码盘点错误信息

  // radio
  const handleRadio = (e) => {
    // 资产不在我手上，跳转无法盘点页面
    if (e === false) {
      history.push({
        pathname: '/inventory/self/noInventory/' + inventoryId + '/' + id.current
      })
    }
    setIsInMe(e)
  }
  // 上传图片
  const handleUploadImage = (fileList) => {
    setImageList(fileList)
  }

  // 备注
  const handleNote = (val) => {
    setNote(val)
  }

  // 提交盘点接口
  const submitInventory = useCallback(
    (images = []) => {
      API.submitInventory({
        asset_info: {
          id: id.current,
          check_result: 1,
          is_transfer: false,
          check_remark: note
        },
        check_pic: images
      }).then(res => {
        Toast.show('盘点成功！')
        // 盘点结束跳转盘点资产列表，inventoryId：盘点计划ID
        history.push({
          pathname: '/inventory/self/list/' + inventoryId.current
        })
      }).catch(err => {
        Toast.show({
          icon: 'fail',
          content: err.msg || '盘点失败！',
        })
      })
    },
    [id, note, inventoryId, history]
  )

  // 提交盘点
  const handleSubmitInventory = useCallback(
    () => {
      if (!imageList.length) {
        Toast.show('请上传资产图片进行盘点')
        return
      }
      let resultImg = _.cloneDeep(imageList)
      resultImg = resultImg.map(item => ({
        picture_url: item.url,
        ...(userInfo?.realname ? { created_realname: userInfo?.realname } : {})
      }))
      submitInventory(resultImg)
    },
    [imageList, userInfo, submitInventory]
  )

  // 获取资产盘点详情
  const getInventoryAssetsDetail = () => {
    API.getInventoryAssetsDetail({
      plan_id: Number(inventoryId.current),
      asset_code: assetCode.current
    }).then(res => {
      const {
        asset_info: assetInfo = {},
        check_pic: checkPic = [],
        check_result_flag: checkResultFlag = {}
      } = res.data
      if (!_.isEmpty(assetInfo)) {
        id.current = assetInfo?.id
        const infoList = getAssetsBaseInfos(SELF_INVENTORY)
        infoList.forEach(item => {
          if (item.key === 'picture_url') { // 图片
            item.value = checkPic
          } else {
            item.value = (assetInfo[item.key] || assetInfo[item.key] === 0) ? assetInfo[item.key] : '-'
          }
        })
        setInventoryShowData(infoList)
        // setAssetCode(assetInfo?.asset_code)
        setInventory(checkResultFlag)
        setAssetStatus(assetInfo?.check_result)
      }
    }).catch(err => {
      // Toast.show({
      //   icon: 'fail',
      //   content: err.msg || `${assetCode.current} 盘点失败`,
      // })
      setInventoryErrMsg(err.msg || `${assetCode.current} 盘点失败`)
    })
  }

  const inventoryBtnDisabled = useCallback(() => {
    return (
      !imageList.length ||
      assetStatus > 0 ||
      (!_.isEmpty(inventoryFlag) && !inventoryFlag?.can_commit)
    )
  }, [imageList, assetStatus, inventoryFlag])

  const renderInventoryButton = useMemo(
    () => (
      <div>
        {
          !_.isEmpty(inventoryFlag) && !inventoryFlag?.can_commit &&
            <span className='no-inventory-prompt'>{inventoryFlag?.error_commit_msg || '无法盘点，请联系行政！'}</span>
        }
        <Button
          block
          color="primary"
          className='scan-code'
          disabled={inventoryBtnDisabled()}
          onClick={handleSubmitInventory}
        >
          {assetStatus > 0 ? '已盘点' : '提交盘点'}
        </Button>
      </div>
    ),
    [assetStatus, inventoryFlag, inventoryBtnDisabled, handleSubmitInventory]
  )

  const renderInventoryErrMsg = useMemo(
    () => {
      if (!inventoryErrMsg) return null
      return (
        <div className='inventory-err-msg'>
          <ExclamationShieldOutline fontSize={48} />
          <span className='err-msg-content'>{inventoryErrMsg}</span>
        </div>
      )
    },
    [inventoryErrMsg]
  )

  useEffect(() => {
    setTitle('盘点详情')
    getInventoryAssetsDetail()
  }, [])

  return <div className='self-inventory-detail'>
    <AssetDetail
      assetCode={assetCode.current}
      assetStatus={assetStatus}
      inventoryShowData={inventoryShowData}
    />
    {
      assetStatus <= 0 ? (
        <div className='asset-in-me'>
          <h2 className='result-title'>以下信息，请如实填写</h2>
          <Radio.Group onChange={handleRadio}>
            <Space direction='vertical'>
              <Radio value={true}>资产在我手上，正常盘点</Radio>
              <Radio value={false}>资产不在我手上，无法盘点</Radio>
            </Space>
          </Radio.Group>
        </div>
      ) : null
    }
    {
      isInMe ? <>
        <AssetPerfect
          isRequiredImage={true}
          note={note}
          imageList={imageList}
          imagePrompt={IMGAGE_PROMPT}
          handleNote={handleNote}
          handleUploadImage={handleUploadImage}
        />
        {
          renderInventoryButton
        }
      </> : null
    }
    {
      renderInventoryErrMsg
    }
  </div>
}

export default SelfInventoryDetail
