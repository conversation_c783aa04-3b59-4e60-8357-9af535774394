import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { Button } from 'antd-mobile'
import { Toast, Grid } from 'antd-mobile-v5'
import AssetDetail from '../AssetDetail'
import AssetPerfect from '../AssetPerfect'
import setTitle from 'components/setBarTitle'
import { dingScanCode } from 'utils/dingDing'
import _ from 'lodash'
import API from '../../apis'
import { getAssetsBaseInfos, ADMINISTRATIVE_INVENTORY } from '../../constants'
import { SCAN_INVENTORY, SAVE_INVENTORY, CANCEL_INVENTORY } from './constant'
import './index.less'
// import * as dd from 'dingtalk-jsapi'

let scanTag = ''
const AdministrativeInventory = (props) => {
  const userInfo = JSON.parse(window.localStorage.getItem('user') || '{}')
  const planId = props?.match?.params?.plan_id || '' // 盘点计划id
  const [isShow, setIsShow] = useState(false) // 是否显示资产信息
  const [loading, setLoading] = useState(false)
  const [inventoryShowData, setInventoryShowData] = useState([])
  const [assetCode, setAssetCode] = useState('') // 资产编码
  const [assetStatus, setAssetStatus] = useState(undefined) // 资产状态
  const [note, setNote] = useState('') // 补充备注信息
  const [imageList, setImageList] = useState([]) // 补充图片信息

  const initPageVal = () => {
    setInventoryShowData([])
    setAssetCode('')
    setAssetStatus(undefined)
    setNote('')
    setImageList([])
  }

  // 上传图片
  const handleUploadImage = (fileList) => {
    setImageList(fileList)
  }

  // 备注
  const handleNote = (val) => {
    setNote(val)
  }

  // 点击扫码
  const handleSancode = () => {
    // if (dd.env.platform === 'notInDingTalk') {
    //   const num = '0000004076'
    //   // getInventoryAssetsDetail(num)
    //   adminSumitInventory(SCAN_INVENTORY, {
    //     assetCode: num
    //   })
    //   scanTag = num
    // }
    dingScanCode(code => {
      scanTag = code
      // Toast.show(code)
      // getInventoryAssetsDetail(code)
      adminSumitInventory(SCAN_INVENTORY, {
        assetCode: code
      })
    }, error => {
      if (!error?.errorMessage) return
      Toast.show({
        icon: 'fail',
        content: error.errorMessage,
      })
    })
  }

  /**
   * 盘点计划-行政执行盘点提交
   * @param {Boolean} type 类型,true：点击保存,false:扫码提交盘点
   * @param {Object} params 参数
   * @param {Boolean} type 类型
   */
  const adminSumitInventory = useCallback(
    (type, params = {}) => {
      const {
        assetCode = scanTag,
        images = []
      } = params
      const resultObj = {
        asset_info: {
          asset_code: assetCode,
          plan_id: Number(planId),
          ...(type !== SCAN_INVENTORY ? { // 备注，扫码盘点不用传
            admin_check_remark: note,
          } : {}),
          ...(type !== SAVE_INVENTORY ? { // 状态，保存的时候不用传
            check_result: type === SCAN_INVENTORY ? 1 : -1
          } : {})
        },
        ...(type === SAVE_INVENTORY ? { // 图片，保存的时候传
          check_pic: images
        } : {})
      }
      API.adminSumitInventory(resultObj).then(res => {
        if (type === SCAN_INVENTORY) {
          if (typeof res.data === 'number') {
            getInventoryAssetsDetail(res.data)
          }
          Toast.show(`资产编码：${assetCode} 盘点完成`)
        } else {
          Toast.show(type === SAVE_INVENTORY ? '保存成功！' : '取消盘点成功！')
          setIsShow(false)
          setLoading(false)
          initPageVal()
        }
      }).catch(err => {
        if (type) {
          Toast.show({
            icon: 'fail',
            content: err.msg || '保存失败！',
          })
          setTimeout(() => {
            setIsShow(false)
            setLoading(false)
            initPageVal()
          }, 1000)
        } else {
          Toast.show({
            icon: 'fail',
            content: err.msg || '扫码盘点失败！',
          })
        }
      })
    },
    [note, planId]
  )

  // 获取资产盘点详情
  const getInventoryAssetsDetail = (id) => {
    API.getInventoryAssetsDetail({ id: Number(id) }).then(res => {
      const { asset_info: assetInfo = {}, check_pic: checkPic = [] } = res.data
      if (!_.isEmpty(assetInfo)) {
        const infoList = getAssetsBaseInfos(ADMINISTRATIVE_INVENTORY)
        infoList.forEach(item => {
          if (item.key === 'picture_url') { // 图片
            item.value = checkPic
          } else {
            item.value = (assetInfo[item.key] || assetInfo[item.key] === 0) ? assetInfo[item.key] : '-'
          }
        })
        setIsShow(true)
        setInventoryShowData(infoList)
        setAssetCode(assetInfo?.asset_code)
        setAssetStatus(assetInfo?.check_result)
      }
    }).catch(err => {
      Toast.show({
        icon: 'fail',
        content: err.msg,
      })
    })
  }

  const getImgList = useCallback(
    () => {
      let resultImg = _.cloneDeep(imageList || [])
      resultImg = resultImg.map(item => ({
        picture_url: item.url,
        ...(userInfo?.realname ? { created_realname: userInfo?.realname } : {})
      }))
      return resultImg
    },
    [imageList, userInfo]
  )

  // 取消盘点
  const handleCancelInventory = useCallback(() => {
    adminSumitInventory(CANCEL_INVENTORY, {
      assetCode: scanTag,
      images: getImgList()
    })
  }, [adminSumitInventory, getImgList])

  // 提交盘点
  const handleSubmitInventory = useCallback(() => {
    setLoading(true)
    adminSumitInventory(SAVE_INVENTORY, {
      assetCode: scanTag,
      images: getImgList()
    })
  }, [adminSumitInventory, getImgList])
  // 提交盘点接口
  const submitInventory = (images = []) => {
    API.submitInventory({
      asset_info: {
        id: scanTag,
        check_result: assetStatus,
        is_transfer: false,
        check_remark: note
      },
      check_pic: images
    }).then(res => {
      Toast.show('盘点成功！')
      setIsShow(false)
      setLoading(false)
      initPageVal()
    }).catch(err => {
      Toast.show({
        icon: 'fail',
        content: err.msg || '盘点失败！',
      })
      setTimeout(() => {
        setIsShow(false)
        setLoading(false)
        initPageVal()
      }, 1000)
    })
  }

  const renderButton = useMemo(
    () => (
      <Grid columns={2} gap={8}>
        <Grid.Item>
          <Button
            type="warning"
            inline
            className='scan-code'
            onClick={handleCancelInventory}
          >
            取消盘点
          </Button>
        </Grid.Item>
        <Grid.Item>
          <Button
            type="primary"
            inline
            className='scan-code'
            activeClassName='active-scan-code'
            loading={loading}
            disabled={(!note && !imageList.length) || loading}
            onClick={handleSubmitInventory}
          >
            保存
          </Button>
        </Grid.Item>
      </Grid>
    ),
    [loading, note, imageList, handleCancelInventory, handleSubmitInventory]
  )

  useEffect(() => {
    setTitle('行政扫码盘点')
  }, [])

  return <div className='administrative-inventory'>
    <Button
      icon={<img src='https://img.ikstatic.cn/MTY1MDQ1MDAxNDgwMyMxOTcjcG5n.png' className='scan-code-icon' alt='' />}
      type="primary"
      className='scan-code'
      activeClassName='active-scan-code'
      onClick={handleSancode}
    >
      扫码盘点
    </Button>
    {
      isShow ? (
        <>
          <div className='asset-result'>
            <h2 className='result-title'>扫码资产结果：</h2>
            <AssetDetail
              assetCode={assetCode}
              assetStatus={assetStatus}
              inventoryShowData={inventoryShowData}
            />
            <h2 className='result-title'>补充盘点信息：</h2>
            <span className='perfect-operation-tip'>盘点结果已实时上传，没有补充信息时无需点保存按钮</span>
            <AssetPerfect
              note={note}
              imageList={imageList}
              handleNote={handleNote}
              handleUploadImage={handleUploadImage}
            />
          </div>
          {
            renderButton
          }
        </>
      ) : null
    }
  </div>
}

export default AdministrativeInventory
