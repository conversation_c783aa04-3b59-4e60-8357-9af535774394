import React, { useEffect, useState } from 'react'
import { Tag, Toast, ImageViewer } from 'antd-mobile-v5'
import { inventoryResultStatus, inventoryResultStatusColor } from '../../constants'
import _ from 'lodash'
import './index.less'
import { render } from 'less'

const AssetDetail = (props) => {
  const assetStatus = props?.assetStatus
  const assetCode = props?.assetCode || ''
  const assetDetailInfos = props?.inventoryShowData || []
  const [isImgVisible, setIsImgVisible] = useState(false)
  const [imageSrc, setImageSrc] = useState('')

  const renderItemValue = (item) => {
    if (item.key === 'picture_url') {
      if (!_.isEmpty(item.value)) {
        return <div className='asset-img-wrapper'>
          {
            item.value.map((e, index) => (
              <img
                key={index}
                alt=''
                className='asset-img'
                src={e.picture_url}
                onClick={() => {
                  if (e.picture_url) {
                    setIsImgVisible(true)
                    setImageSrc(e.picture_url)
                  }
                }}
              />
            ))
          }
        </div>
      } else {
        return <span>-</span>
      }
    } else if (item.key === 'check_result') {
      return <Tag color={inventoryResultStatusColor[item.value]}>{(item.value || item.value === 0) ? inventoryResultStatus[item.value] : '-'}</Tag>
    } else {
      return <span>{item.value || '-'}</span>
    }
  }

  return <div className='asset-detail'>
    <ImageViewer
      image={imageSrc}
      visible={isImgVisible}
      onClose={() => {
        setIsImgVisible(false)
      }}
    />
    {
      !_.isEmpty(assetDetailInfos) ? (
        <div className='result-infos'>
          <div className='infos-serial-number'>
            <div className='serial-number-left'>
              <Tag color={inventoryResultStatusColor[assetStatus]}>{inventoryResultStatus[assetStatus]}</Tag>
              <span className='serial-number-val' onClick={() => {
                if (assetCode) {
                  Toast.show({ content: assetCode })
                }
              }}>{assetCode}</span>
            </div>
          </div>
          <div className='inventory-info-detail'>
            {
              assetDetailInfos.map((item, i) => (
                <div key={i} className='every-type-content'>
                  <span className='every-type-label'>{item.label}</span>
                  <span>：</span>
                  {
                    renderItemValue(item)
                  }
                </div>
              ))
            }
          </div>
        </div>
      ) : null
    }
  </div>
}

export default AssetDetail
