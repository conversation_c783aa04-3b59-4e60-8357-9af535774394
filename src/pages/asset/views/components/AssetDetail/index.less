.asset-detail {
  .result-infos {
    background: #ffffff;
    border-radius: 15px;
    box-shadow: 1px 5px 13px 1px #dfe3e3;
    height: auto;
    margin-bottom: 20px;
  }
  .infos-serial-number {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #F2F2F2;
    padding: 0px 20px;
    height: 100px;
  }
  .serial-number-left {
    display: flex;
    max-width: 100%;
    .serial-number-val {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-left: 20px;
    }
  }
  .inventory-info-detail {
    padding: 20px;
  }
  .every-type-content {
    margin: 20px 0px;
    .every-type-label {
      display: inline-block;
      font-family: PingFangSC-Medium, PingFang SC;
      // width: 130px;
      // font-size: 32px;
      // min-width: 130px;
      // text-align: justify;
      // text-justify: inter-ideograph;
			// text-align-last: justify;
    }
  } 
}
.asset-img-wrapper {
  height: 200px;
  overflow-y: scroll;
  // text-overflow: ellipsis;
  // display: -webkit-box;
  // -webkit-line-clamp: 2;
  // -webkit-box-orient: vertical;
  .asset-img {
    width: 110px;
    height: 110px;
    margin: 11px 11px 11px 11px;
  }
}
