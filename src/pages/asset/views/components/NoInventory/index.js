/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-4-23
 * @Description: 无法盘点
 */
import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react'
import { Radio, TextArea, Button, Toast, Input } from 'antd-mobile-v5'
import setTitle from 'components/setBarTitle'
import PopupPeople from 'components/PopupPeople'
import { CloseCircleOutline } from 'antd-mobile-icons'
import API from '../../apis'
import _API from './apis'
import { isEmpty } from 'lodash'
import './index.less'
import { useHistory } from 'react-router-dom'

const NoInventory = (props) => {
  const id = props.match.params.id
  const inventoryId = props.match.params.inventoryId
  const selectPeopleRef = useRef(null)
  const history = useHistory()

  const [note, setNote] = useState('') // 备注
  const [selectPeopleVisible, setSelectPeopleVisible] = useState(false) // 转发人popup
  const [allPeople, setAllPeople] = useState([])
  const [selectPeopleInfo, setSelectPeopleInfo] = useState(null)
  const [isTransfer, setIsTransfer] = useState(true)

  const handleRadio = (val) => {
    setIsTransfer(val)
  }

  // 取消选择人
  const handleCancal = useCallback(() => {
    setSelectPeopleVisible(!selectPeopleVisible)
  }, [selectPeopleVisible])

  const handleOk = useCallback((value) => {
    handleCancal()
    setSelectPeopleInfo(!isEmpty(value) ? {
      to_check_user: value[0]?.login_name,
      to_check_realname: value[0]?.real_name
    } : null)
  }, [handleCancal])

  // 提交盘点
  const handleSubmitInventory = () => {
    if (isTransfer && isEmpty(selectPeopleInfo)) {
      Toast.show('请选择转发人！')
      return
    }
    submitInventory()
  }

  // 点击选择转发人
  const handleClickTransfer = () => {
    setSelectPeopleVisible(true)
    selectPeopleRef.current.blur()
  }

  // 清除转发人
  const handleClearTransfer = (e) => {
    setSelectPeopleInfo(null)
  }

  const renderTransfer = useMemo(
    () => {
      if (!isTransfer) {
        return null
      }
      return (
        <div className='turn-user'>
          <span className='turn-user-title'>转给Ta：</span>
          <Input
            ref={selectPeopleRef}
            placeholder='选择转发人'
            className='select-transfer'
            value={selectPeopleInfo?.to_check_realname ?? ''}
            onClick={handleClickTransfer}
          />
          {
            !isEmpty(selectPeopleInfo)
              ? (
                <CloseCircleOutline
                  className='clear-icon'
                  fontSize={28}
                  onClick={handleClearTransfer}
                />
              )
              : null
          }
        </div>
      )
    },
    [isTransfer, selectPeopleInfo]
  )

  // 提交盘点接口
  const submitInventory = () => {
    API.submitInventory({
      asset_info: {
        id: Number(id),
        is_transfer: isTransfer,
        check_result: isTransfer ? -1 : 2,
        check_remark: note,
        ...(isTransfer ? selectPeopleInfo : {})
      },
    }).then(res => {
      Toast.show('盘点成功！')
      // 盘点结束跳转盘点资产列表，inventoryId：盘点计划ID
      history.push({
        pathname: '/inventory/self/list/' + inventoryId
      })
    }).catch(err => {
      Toast.show({
        icon: 'fail',
        content: err.msg || '提交失败！',
      })
    })
  }

  // 获取所有人员
  const getAllPeople = () => {
    _API.getAllPeople().then(res => {
      setAllPeople(res.data || [])
    })
  }

  useEffect(() => {
    setTitle('无法盘点')
    getAllPeople()
  }, [])

  return <div className='no-inventory'>
    {
      useMemo(
        () => (
          <PopupPeople
            visible={selectPeopleVisible}
            value={selectPeopleInfo?.to_check_user ?? ''}
            allPeople={allPeople}
            onCancal={handleCancal}
            onOk={handleOk}
          />
        ),
        [
          selectPeopleVisible,
          selectPeopleInfo,
          allPeople,
          handleCancal,
          handleOk
        ]
      )
    }
    <div className='no-inventory-wrapper'>
      <h2 className='result-title'>异常类型：</h2>
      <div className='select-radio'>
        <Radio.Group value={isTransfer} onChange={handleRadio}>
          <Radio value={true} className='radio-turn'>转发盘点</Radio>
          <Radio value={false}>其它异常</Radio>
        </Radio.Group>
      </div>
      {/* 转发人员 */}
      {
        renderTransfer
      }
      <div className='note'>
        {/* <span className='more-operation-title'>备注:</span> */}
        <TextArea
          className='note-textarea'
          placeholder='请输入备注(选填)'
          rows={3}
          showCount
          maxLength={150}
          value={note}
          onChange={(val) => setNote(val)}
        />
      </div>
    </div>
    <Button
      block
      color="primary"
      className='btn-operation'
      onClick={handleSubmitInventory}
    >
      提交
    </Button>
  </div>
}

export default NoInventory
