.no-inventory {
  background: #F0F5F5;
  min-height: 100vh;
  padding: 20px;
  .no-inventory-wrapper {
    background: #ffffff;
    border-radius: 15px;
    margin: 20px 0px 30px;
    padding: 20px;
    box-shadow: 1px 5px 13px 1px #dfe3e3;
    .result-title {
      font-size: 36px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
    }
    .select-radio {
      margin: 20px 0px;
    }
    .radio-turn {
      margin-right: 50px;
    }
  }
  .turn-user {
    display: flex;
    align-items: center;
    margin: 30px 0px;
    .search {
      flex: 1;
    }
    .turn-user-title {
      display: inline-block;
      min-width: max-content;
      font-size: 36px;
    }
    .select-transfer {
      border: 1px solid #eee;
      padding: 6px 10px;
      border-radius: 10px;
    }
    .clear-icon {
      margin-left: 10px;
    }
  }
  .note {
    // margin-bottom: 20px;
    .note-textarea {
      border: 1px solid #F2F2F2;
      padding: 0 10px;
      border-radius: 10px;
    }
  }
  .btn-operation {
    --background-color: #00d8c9;
    --border-color: #00d8c9;
  }
}