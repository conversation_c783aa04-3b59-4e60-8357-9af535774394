import React, { useCallback, useEffect, useState } from 'react'
import { Tag } from 'antd-mobile-v5'
import Empty from 'components/Empty'
import setTitle from 'components/setBarTitle'
import { MY_ASSET_LIST, getAssetsBaseInfos, assetStatus, assetStatusColor } from '../../constants'
import API from '../../apis'
import _ from 'lodash'
import './index.less'

const MyAssetList = (props) => {
  const [type, setType] = useState('') // 资产列表类型
  const [assetShowData, setAssetShowData] = useState([])

  // 获取我的资产列表
  const getMyAssetList = useCallback(
    () => {
      if (!type) return
      API.getMyAssetList({
        ...(type !== 'all_cnt' ? {
          status: type === 'use_cnt' ? 1 : 2
        } : {})
      }).then(res => {
        const infoList = getAssetsBaseInfos(MY_ASSET_LIST)
        const data = res.data || []
        if (data && data.length) {
          const resultArr = []
          data.forEach(item => {
            const tempObj = {
              id: item.id,
              code: item.code,
              status: item.status
            }
            const itemArr = _.cloneDeep(infoList)
            itemArr.forEach(ele => {
              if (ele.key === 'asset_name') {
                ele.value = item['name'] || '-'
              } else if (ele.key === 'receive_time') { // 领用时间
                ele.value = item[ele.key] ? new Date(item[ele.key] * 1000).format('yyyy-MM-dd hh:mm:ss') : '-'
              } else {
                ele.value = (item[ele.key] || item[ele.key] === 0) ? item[ele.key] : '-'
              }
            })
            resultArr.push({
              ...tempObj,
              asset_info: itemArr
            })
          })
          setAssetShowData(resultArr)
        }
      }).catch(err => {
        console.log(err)
      })
    },
    [type]
  )

  useEffect(() => {
    const listType = props.match.params.type
    if (listType === 'use_cnt') {
      setTitle('资产列表-使用中')
    } else if (listType === 'borrow_cnt') {
      setTitle('资产列表-借用中')
    } else {
      setTitle('资产列表-全部')
    }
    setType(listType || '')
    getMyAssetList()
  }, [props.match.params.type, getMyAssetList])

  return <div className='asset-wrapper'>
    {
      assetShowData.length ? assetShowData.map((element, index) => (
        <div className='asset-list' key={index}>
          <div className='asset-list-top'>
            <span className='asset-num'>资产编码: {element.code}</span>
            <Tag color={assetStatusColor[element.status]} className='asset-tag' fill='outline'>{assetStatus[element.status]}</Tag>
          </div>
          <div className='attribute-list'>
            {
              !_.isEmpty(element.asset_info) && element.asset_info.map((item, i) => (
                <div key={i} className='every-type-content'>
                  <span className='every-type-label'>{item.label}</span>
                  <span>：</span>
                  <span>{item.value || '-'}</span>
                </div>
              ))
            }
          </div>
        </div>
      )) : <Empty describe='暂无数据' />
    }
  </div>
}

export default MyAssetList
