import React from 'react'
import { Text<PERSON><PERSON>, ImageUploader, Toast } from 'antd-mobile-v5'
import axios from 'axios'
import './index.less'
const instance = axios.create()

const AssetPerfect = (props) => {
  const {
    isRequiredImage = false, // 上传图片必须
    note = '',
    imageList: fileList = [],
    imagePrompt = ''
  } = props

  // 上传方法
  const myUploadFn = async (param) => {
    const serverURL = `https://upload.inkept.cn/upload/media`
    const objData = await instance({
      url: serverURL,
      method: 'post',
      data: param,
      timeout: 1000 * 60 * 5
    }).catch(err => {
      Toast.show({
        icon: 'fail',
        content: err.error_msg,
      })
    })
    if (objData.data.dm_error !== 0) {
      Toast.show({
        icon: 'fail',
        content: objData.data.error_msg,
      })
      return {}
    }
    return objData.data
  }

  return <div className='asset-perfect'>
    <div className='note'>
      <span className='more-operation-title'>盘点备注:</span>
      <TextArea
        className='note-textarea'
        placeholder='请输入内容'
        rows={3}
        showCount
        maxLength={150}
        value={note}
        onChange={(val) => {
          if (typeof props.handleNote === 'function') {
            props.handleNote(val)
          }
        }}
      />
    </div>
    <div className='inventory-perfect-img'>
      <span className={isRequiredImage ? 'more-operation-title is_required' : 'more-operation-title'}>盘点图片:</span>
      <ImageUploader
        value={fileList}
        // capture={true}
        onChange={(e) => {
          if (typeof props.handleUploadImage === 'function') {
            props.handleUploadImage(e)
          }
        }}
        upload={myUploadFn}
      />
      {
        imagePrompt ? <span className='image-prompt-inventory'>图片要求：{imagePrompt}</span> : null
      }
    </div>
  </div>
}

export default AssetPerfect
