.self-inventory {
  background: #F0F5F5;
  min-height: 100vh;
  padding: 20px;
  .inventory-list-box {
    background: #FFFFFF;
    border-radius: 15px;
    box-shadow: 1px 5px 13px 1px #dfe3e3;
    margin-bottom: 20px;
    .inventory-title {
      min-height: 80px;
      border-bottom: 2px solid #F2F2F2;
      font-size: 30px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #00d8c9;
      padding: 20px 20px 6px 20px;
      display: flex;
      align-items: center;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      // line-height: 80px;
    }
    .inventory-content {
      display: flex;
      flex-direction: column;
      padding: 10px 20px 30px 20px;
    }
    .inventory-status {
      margin-bottom: 20px;
    }
    .inventorying {
      display: inline-block;
      background: #6e9cee;
      color: #ffffff;
      padding: 2px 20px;
      border-radius: 10px;
    }
  }
}
.empty-val {
  width: max-content;
  position: relative;
  left: 50%;
  top: 50%;
  margin-top: -108px;
  transform: translate(-50%, -50%);
}