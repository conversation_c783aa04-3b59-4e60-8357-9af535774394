import React, { useCallback, useEffect, useState } from 'react'
import { Tag } from 'antd-mobile-v5'
import Empty from 'components/Empty'
import setTitle from 'components/setBarTitle'
import { INVENTORY_EMPTY_TIP, ADMINISTRATIVE_INVENTORY } from '../../constants'
import { useHistory } from 'react-router-dom'
import API from '../../apis'
import $utils from 'utils'
import './index.less'

const SelfInventory = (props) => {
  const history = useHistory()
  const inventoryType = $utils.getUrlQuery('type') || '' // 盘点类型
  const [inventoryList, setInventoryList] = useState([])

  // 跳转到盘点资产列表
  const handleJumpDetail = (id) => {
    if (inventoryType === ADMINISTRATIVE_INVENTORY) { // 行政盘点
      history.push({
        pathname: '/inventory/administrative/' + id
      })
    } else {
      history.push({
        pathname: '/inventory/self/list/' + id
      })
    }
  }

  // 获取盘点单列表
  const getSelfInventoryList = useCallback(() => {
    API.getSelfInventoryList({
      type: inventoryType === ADMINISTRATIVE_INVENTORY ? 2 : 1
    }).then(res => {
      setInventoryList(res.data || [])
    }).catch(err => {
      console.log(err)
    })
  }, [inventoryType])

  useEffect(() => {
    setTitle('盘点单列表')
    getSelfInventoryList()
  }, [getSelfInventoryList])

  return <div className='self-inventory'>
    {
      inventoryList.length ? inventoryList.map((item, i) => (
        <div key={i} className='inventory-list-box' onClick={() => handleJumpDetail(item.id)}>
          <h2 className='inventory-title'>{item?.plan_name}</h2>
          <div className='inventory-content'>
            <div className='inventory-status'>
              <span>盘点状态：</span>
              {/* <span className='inventorying'>进行中</span> */}
              <Tag color="#6e9cee">进行中</Tag>
            </div>
            <span className='inventory-date'>截止日期：{item?.prompt_end_date}</span>
          </div>
        </div>
      )) : <Empty describe={INVENTORY_EMPTY_TIP} />
    }
  </div>
}

export default SelfInventory
