// 盘点无数据提示
export const INVENTORY_EMPTY_TIP = '暂无需盘点数据'

// 默认无数据提示
export const DEFAULT_EMPTY_TIP = '暂无数据'

// 盘点资产列表-顶部通告
export const TOP_NOTICE_BAR = '请及时执行盘点，有疑问请联系行政！！！'

// 自助盘点上传盘点图片提示
export const IMGAGE_PROMPT = '请拍摄设备正面全身照，电子设备保持开机点亮状态！'

// 资产基本信息
export const assetBaseInfos = [{
  key: 'category_name',
  label: '资产分类',
  value: '',
}, {
  key: 'asset_name',
  label: '资产名称',
  value: '',
}, {
  key: 'brand',
  label: '品牌',
  value: '',
}, {
  key: 'model',
  label: '型号',
  value: '',
}, {
  key: 'configuration',
  label: '配置',
  value: '',
},
// {
//   key: 'place',
//   label: '位置',
//   value: '北京',
// },
{
  key: 'receive_realname',
  label: '使用人',
  value: '',
}, {
  key: 'receive_dept_name',
  label: '使用部门',
  value: '',
}]

// 自助盘点资产列表
export const selfInventoryDetailData = [
  {
    key: 'check_result',
    label: '盘点状态',
    value: '',
  },
  ...assetBaseInfos
]

// 行政扫码盘点，扫码盘点展示资产详情信息
export const adminInventoryDetailData = [
  ...assetBaseInfos,
  {
    key: 'picture_url',
    label: '资产图片',
    value: '',
  },
  {
    key: 'check_result',
    label: '盘点结果',
    value: '',
  }
]

export const myAssetList = [
  ...assetBaseInfos,
  {
    key: 'receive_time',
    label: '领用日期',
    value: '',
  },
  {
    key: 'store_address',
    label: '存放地点',
    value: '',
  },
]

export const selfInventoryData = [
  ...assetBaseInfos,
  {
    key: 'check_remark',
    label: '盘点备注',
    value: '',
  },
  {
    key: 'picture_url',
    label: '资产图片',
    value: '',
  },
]

export const SELF_INVENTORY = 'self_inventory' // 自助盘点
export const SELF_INVENTORY_ASSET_LIST = 'self_inventory_asset_list' // 自助盘点资产列表
export const ADMINISTRATIVE_INVENTORY = 'administrative_inventory' // 行政盘点
export const MY_ASSET_LIST = 'my_asset_list' // 我的资产

// 获取资产详情信息
export const getAssetsBaseInfos = (type) => {
  if (type === SELF_INVENTORY) { // 自助盘点
    // return assetBaseInfos
    return selfInventoryData
  } else if (type === ADMINISTRATIVE_INVENTORY) { // 行政盘点
    return adminInventoryDetailData
  } else if (type === SELF_INVENTORY_ASSET_LIST) {
    return selfInventoryDetailData
  } else if (type === MY_ASSET_LIST) { // 我的资产
    return myAssetList
  } else {
    return []
  }
}

// 盘点结果状态
export const inventoryResultStatus = {
  '-1': '未盘点',
  0: '未盘点',
  1: '已盘点',
  2: '异常无法盘点',
  3: '盘盈',
  4: '盘亏',
}

// 盘点结果状态标签颜色
export const inventoryResultStatusColor = {
  '-1': '#ff8f1f',
  0: '#ff8f1f',
  1: '#00d8c9',
  2: '#ff8f1f',
  3: '#00d8c9',
  4: '#ff3141',
}

// 资产状态
export const assetStatus = {
  0: '空闲',
  1: '领用',
  2: '借用',
  3: '处置'
}

// 资产状态标签颜色
export const assetStatusColor = {
  0: '#9dc96b',
  1: '#f5c800',
  2: '#ec635d',
  3: '#ff8d31'
}

// 盘点列表tabs

export const ALL_LIST = 'all_list' // 全部资产列表
export const NO_INVENTORY_LIST = 'no_inventory_list' // 未盘点列表
export const YES_INVENTORY_LIST = 'yes_inventory_list' // 已盘点列表
export const inventoryListTabs = [
  {
    title: '全部',
    key: ALL_LIST,
    interfaceField: 'all_cnt',
    num: 0,
    list: []
  },
  {
    title: '未盘点',
    key: NO_INVENTORY_LIST,
    interfaceField: 'need_cnt',
    num: 0,
    list: []
  },
  {
    title: '已盘点',
    key: YES_INVENTORY_LIST,
    interfaceField: 'finish_cnt',
    num: 0,
    list: []
  }
]
