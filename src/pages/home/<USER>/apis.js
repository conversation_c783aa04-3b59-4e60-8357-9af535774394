/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-25 10:00:40
 * @LastEditTime: 2020-11-25 20:30:00
*/

import { getRequestsByRoot } from 'axios-service'
import srcConfig from 'src/config/apiRoots'

const { get, post, postXForm, restFulDelete } = getRequestsByRoot({ root: srcConfig.familyWorkFlow })
class Apis {
  /**
   * 流程发起-表单控件
   */
  authorizationEntry = get('workflow/api/v1/rbac/permission/mobile', {}, { autoLoading: false })
}

export default new Apis()
