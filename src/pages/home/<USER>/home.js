/*
 * @Author: re<PERSON><PERSON><PERSON>
 * @Date: 2020-10-13 11:27:28
 * @LastEditTime: 2020-10-15 20:28:47
 * @LastEditors: Please set LastEditors
 * @Description: 首页
 * @FilePath: /my-app/Users/<USER>/code/family-h5/src/pages/home/<USER>/home.js
 */
import React from 'react'
import { Modal, Toast } from 'antd-mobile'
import './home.less'
// import NavTopBar from 'components/NavTopBar'
import setTitle from 'components/setBarTitle'
import Confirm from 'components/confirm'
import API from './apis'
const alert = Modal.alert

class Home extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      // itemData: []
      itemData: [
        // {
        //   icon: require('assets/home/<USER>'),
        //   name: '订餐',
        //   url: window.location.origin + '/order/index.html'
        // },
        {
          icon: require('assets/home/<USER>'),
          name: '流程审批',
          url: window.location.origin + '/flow/index.html#/contract'
        }
      // {
      //   icon: require('assets/home/<USER>'),
      //   name: '流程审批',
      //   url: window.location.origin + '/flow/index.html#/contract'
      // },
      // {
      //   icon: require('assets/home/<USER>'),
      //   name: '访客预约',
      //   url: window.location.origin + '/visitor/index.html'
      // }
      ]
    }
  }

  componentDidMount () {
    this.authorizationEntry()
    setTitle('Family')
  }

  authorizationEntry = () => {
    API.authorizationEntry().then(res => {
      if (res.data) {
        this.setState({
          itemData: [
            // {
            //   icon: require('assets/home/<USER>'),
            //   name: '订餐',
            //   url: window.location.origin + '/order/index.html'
            // },
            {
              icon: require('assets/home/<USER>'),
              name: '流程审批',
              url: window.location.origin + '/flow/index.html#/contract'
            }
          ]
        })
      }
    }).catch(err => {
      Confirm.ToastMessage(err.response.data.msg)
    })
  }

  jumpFun = (url) => {
    window.location.href = url
  }

  render () {
    const {
      itemData
    } = this.state
    return (
      <div className='home'>
        {/* <NavTopBar title={'Family'} /> */}
        <div className='home-banner'
          // onClick={() => {
          //   window.location.href = 'https://activity.me.inke.cn/892'
          // }}
        >
        </div>
        <ul className='ul-wrapper'>
          {
            itemData.map((item, i) => {
              return <li key={i} className='item-li' onClick={this.jumpFun.bind(this, item.url)}>
                <img alt='' src={item.icon} className='item-icon' />
                <span>{item.name}</span>
              </li>
            })
          }
          <li className='item-li' onClick={() => {
            window.location.href = 'https://activity.me.inke.cn/906'
          }}>
            <img alt='' src='https://img.ikstatic.cn/MTYzMTc2MjQwODk0MiM5MzkjcG5n.png' className='item-icon-inke-time' />
            <span>INKE TIME</span>
          </li>
          <li className='item-li' onClick={() => {
            window.location.href = window.location.origin + '/asset/index.html'
          }}>
            <img alt='' src={require('assets/home/<USER>')} className='item-icon-inke-time' />
            <span>我的资产</span>
          </li>
        </ul>
      </div>
    )
  }
}

export default Home
