import React, { Suspense, lazy } from 'react'
import { <PERSON>, Switch, Hash<PERSON><PERSON>er, Browser<PERSON><PERSON><PERSON>, Link } from 'react-router-dom'
import LoadingView from 'components/_loading'
// const Router = BrowserRouter

// https://reactjs.org/docs/code-splitting.html#reactlazy

const Home = lazy(() => import('../views/Home'))
// const Motion = lazy(() => import('../views/Motion'))
// const ReactHooks = lazy(() => import('../views/ReactHooks'))

const linkStyle = {
  textDecoration: 'underline',
  margin: 10
}

const getRouter = _ => (
  <HashRouter>
    <Suspense fallback={<LoadingView />}>
      {/* 异步引入 */}
      <Route exact path="/" render={props => <Home {...props}/>} />
    </Suspense>
  </HashRouter>
)

export default getRouter
