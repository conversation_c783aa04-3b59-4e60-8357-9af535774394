import { getRequestsByRoot, serviceHocs, version as axiosServiceVersion } from 'axios-service'
import apiRoots from 'src/config/apiRoots'
import { mockGetWxShareConfig, mockGetWxShareConfigDataSync, mockGetWxShareConfigFail } from '../mocks/demo.mock'
import { once } from 'lodash'
import { compose } from 'ramda'
import { messageDeocator, getErrorMsg, setAtomParamsWapper, requestFailErrMsg } from '../../../entry/service-hoc';

console.log('axiosServiceVersion: ', axiosServiceVersion)

const { requestOptsWrapper } = serviceHocs

const {
  get: baseGet, post: basePost, /* postXForm */
} = getRequestsByRoot({ root: apiRoots.baseapi })

const responseKeys = { dataKey: 'data', msgKey: 'error_msg', codeKey: 'dm_error', successCode: '0' }

const get = requestOptsWrapper(baseGet, responseKeys)
const post = requestOptsWrapper(basePost, responseKeys)

const requestWithCustomKeys = fn => requestOptsWrapper(fn, responseKeys)

const customAtomKeys = ['uid', 'sid']
const transformAid = customAtom => {
  if (!customAtom.publisher) {
    customAtom.aid = customAtom.uid
  }
  return customAtom
}

/**
 * compose 这个内容 跟 下面这两段代码等价
 * 多业务场景 建议 上层使用 compose 组合, 而不是叠加业务
 *
 * const get = requestOptsWrapper(baseGet, responseKeys)
 * const getWithAtomAll = setAtomParamsWapper(get)
 *
 * const getWithAtomAll = compose(
 *  fn => requestOptsWrapper(fn, responseKeys)
 *  setAtomParamsWapper
 * )(baseGet)
 */
const getWithAtomAll = setAtomParamsWapper(get)

/**
 * 特定原子参数配置
 *
 * 如果是多业务场景, 可以声明多个变量, 如:
 * const post = requestOptsWrapper(basePost, responseKeys)
 * const postWithAtomCustom = setAtomParamsWapper(post, customAtomKeys, transformAid)
 *
 * 如果页面所有接口都是统一的多业务场景 建议 统一在上层使用 compose 组合, 而不是叠加业务
 * 如下:
 */
const postWithAtomCustom = compose(
  requestWithCustomKeys,
  fn => setAtomParamsWapper(fn, customAtomKeys, transformAid)
)(basePost)

class Apis {
  /**
   * 获取微信config配置get
   */
  getWxShareConfig = once(getWithAtomAll('/app/wx_share_config'))

  /**
   * 获取微信config配置post
   */
  postWxShareConfig = postWithAtomCustom('/app/wx_share_config')

  /**
   * mock微信配置
   */
  @mockGetWxShareConfig
  getWxShareConfigMock = get('/app/wx_share_config')

  getWxShareConfigMockByCompose = compose(
    messageDeocator({ errorMsg: requestFailErrMsg }),
    mockGetWxShareConfigFail
  )(get('/app/wx_share_config'))

  /**
   * 消息装饰器
   * mock微信配置
   */
  @messageDeocator({ successMsg: '获取微信配置成功', errorMsg: getErrorMsg('获取微信配置失败') })
  @mockGetWxShareConfigDataSync
  getWxShareConfigMockDataSync = get('/app/wx_share_config')
}

export default new Apis()
