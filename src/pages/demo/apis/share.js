import { getRequestsByRoot, serviceHocs, version as axiosServiceVersion } from 'axios-service'
import apiRoots from 'src/config/apiRoots'
import { mockGetWxShareConfig, mockGetWxShareConfigDataSync, mockGetWxShareConfigFail } from '../mocks/demo.mock'
import { once } from 'lodash'
import { compose } from 'ramda'
import { inkeCommonRequestWrapper, requestFailErrMsg, noNetworkErrMsg } from '@decorator/service-assister'
import { commonAtomDecorator, activityAtomDecorator, requestFailMsgDecorator, messageDeocator, asyncAtomDecorate } from '@decorator/service'
import { inkeCommonResponseKeys } from '@config/constants'

console.log('axiosServiceVersion: ', axiosServiceVersion)

const {
  get: baseGet, post: basePost, /* postXForm */
} = getRequestsByRoot({ root: apiRoots.baseapi })

const { getErrorMsg, requestOptsWrapper } = serviceHocs

const shareResponseKeys = { ...inkeCommonResponseKeys, successCode: '0' }
const get = requestOptsWrapper(baseGet, shareResponseKeys)
const post = requestOptsWrapper(basePost, shareResponseKeys)

class Apis {
  /**
   * 获取微信config配置get
   */
  @commonAtomDecorator
  getWxShareConfig = once(get('/app/wx_share_config'))

  /**
   * 获取微信config配置post
   */
  // @mockGetWxShareConfig
  @activityAtomDecorator
  @asyncAtomDecorate()
  @asyncAtomDecorate(atom => ({ ...atom, publisherid: atom.anchor_id }))
  postWxShareConfig = post('/app/wx_share_config')

  /**
   * mock微信配置
   */
  @requestFailMsgDecorator
  @mockGetWxShareConfig
  getWxShareConfigMock = get('/app/wx_share_config')

  getWxShareConfigMockByCompose = compose(
    messageDeocator({ errorMsg: noNetworkErrMsg }),
    mockGetWxShareConfigFail
  )(get('/app/wx_share_config'))

  /**
   * 消息装饰器
   * mock微信配置
   */
  @messageDeocator({ successMsg: '获取微信配置成功', errorMsg: getErrorMsg('获取微信配置失败') })
  @mockGetWxShareConfigDataSync
  getWxShareConfigMockDataSync = get('/app/wx_share_config')
}

export default new Apis()
