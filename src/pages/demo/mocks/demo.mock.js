
import { getMockDecorateByEnv } from 'axios-service'
const isDev = process.env.NODE_ENV === 'development'
const mockDecorate = getMockDecorateByEnv(process.env.NODE_ENV === 'development')

export const mockGetWxShareConfigDataSync = mockDecorate((params) => {
  /**
   * 这种构建之后会隔离内容, 减少数据量, 仅剩if后面的东西
   * 如: if (false) {}
   * 在 DefinePlugin 中增加了一个全局变量 __DEV__ = process.env.NODE_ENV === 'development'
   * 在需要判断环境的地方，可以使用这个变量，
   * （模拟了RN中的 __DEV__ ）
   */
  // if (process.env.NODE_ENV === 'development') {
  if (__DEV__) {
    console.log('demo.mock.js: process.env.NODE_ENV')
    const mockjs = require('mockjs')
    // console.log('mockjs: ', mockjs)
    return Promise.resolve({
      "dm_error": "0",
      "error_msg": "ok",
      "data": {"appid":"wxd740dfea36da1f57","timestamp":1548644657,"noncestr":"QRFwpm0ZBkVLpR0z5uZR","signature":"02e287011a0100de0941d4a26c4badefd435a7fa"}
    })
  }
})


export const mockGetWxShareConfig = mockDecorate((params) => {
  /**
   * 注意: 这种isDev是个变量, 构建时不会隔离, 千万不要这样写
   */
  if (isDev) {
    console.log('demo.mock.js: isDev')
    return Promise.resolve({
      "dm_error": "0",
      "error_msg": "ok",
      "data": {"appid":"wxd740dfea36da1f57","timestamp":1548644657,"noncestr":"QRFwpm0ZBkVLpR0z5uZR","signature":"02e287011a0100de0941d4a26c4badefd435a7fa"}
    })
  }
})

export const mockGetWxShareConfigFail = mockDecorate((params) => {
  /**
   * 注意: 这种isDev是个变量, 构建时不会隔离, 千万不要这样写
   */
  if (isDev) {
    return Promise.reject({
      "dm_error": 888,
      "error_msg": '',
      "data": null
    })
  }
})

