import React, { Suspense, lazy } from 'react'
import { <PERSON>, Switch, Hash<PERSON><PERSON>er, Browser<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'

const Router = BrowserRouter

// https://reactjs.org/docs/code-splitting.html#reactlazy

const Home = lazy(() => import('../views/Home'))
const Motion = lazy(() => import('../views/Motion'))
const ReactHooks = lazy(() => import('../views/ReactHooks'))

const linkStyle = {
  textDecoration: 'underline',
  margin: 10
}

const getRouter = _ => (
  <HashRouter>
    <Suspense fallback="loading">
      <div>------------------- links ------------------</div>
      <ul>
        <li style={linkStyle}><Link to="/">home</Link></li>
        <li style={linkStyle}><Link to="/motion">motion</Link></li>
        <li style={linkStyle}><Link to="/other">other</Link></li>
        <li style={linkStyle}><Link to="/hooks">ReactHooks</Link></li>
      </ul>
      <br/>
      <div>------------------- routers ------------------</div>
      <br/>
      {/* 异步引入 */}
      <Route exact path="/" render={props => <Home {...props}/>} />
      <Route exact path="/motion" render={_ => <Motion />} />
      <Route exact path="/hooks" render={_ => <ReactHooks />} />
      {/* 同步引入（页面没有异步引入的话，可以不需要 Suspense） */}
      <Route path="/other" render={() => <div>
        other page
      </div>} />
    </Suspense>
  </HashRouter>
)

export default getRouter
