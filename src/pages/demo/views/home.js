import React from 'react'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'
import * as userActions from '../store/actions/user'
import { Button, Modal } from 'antd-mobile'
import $log from 'utils/log'
import './home.less'

const mapStateToProps = state => ({ userInfo: state.user.userInfo })
const mapDispatchToProps = dispatch => ({ ...bindActionCreators(userActions, dispatch) })

@connect(mapStateToProps, mapDispatchToProps)
class Home extends React.Component {
  state = {
    num: 1
  }
  get _nick () {
    return this.props.userInfo.nick
  }

  _toggleNick = () => {
    if (this._nick === 'libx') {
      this.props.changeNick('lizh')
    } else {
      this.props.changeNick('libx')
    }
  }

  showModal = () => {
    Modal.alert('modal demo', 'how are you? ')
  }

  report = () => {
    $log.clickReport({
      inkewbid: 'click',
      inkewbname: '用户点击按钮'
    })
  }

  render () {
    return (
      <>
        <Button type="primary" onClick={this._toggleNick}>click to change{ this._nick }</Button>
        <br/>
        <Button inline onClick={this.showModal}>To showModal</Button>
        <Button onClick={this.report}>Click Report</Button>
        <div className='test'>这是啥我也不知道</div>
      </>
    )
  }
}

export default Home
