import React from 'react'
import { Button } from 'antd-mobile'
import Animate from 'rc-animate'
import 'src/styles/motion/index.less'

const Box = (props) => {
  return (
    <div
      key="motion-element"
      style={{ backgroundColor: '#00d46d', width: 400, height: 200, display: props.visible ? 'block' : 'none' }}
      role="document"
    >
      animating
    </div>
  )
}

export default class Motion extends React.Component {
  state = {
    transitionName: '',
    visible: false
  }

  handleFadeMotion = () => {
    this.setState({ visible: !this.state.visible, transitionName: 'fade' })
  }

  handleZoomMotion = () => {
    this.setState({ visible: !this.state.visible, transitionName: 'zoom' })
  }

  onAnimateLeave = (e) => {
    console.log('onAnimateLeave: ', e)
  }

  render () {
    const { state } = this

    return <div>
      <Button type="primary" inline onClick={this.handleFadeMotion}>fade motion</Button>
      <br/>
      <Button type="primary" inline onClick={this.handleZoomMotion}>zoom motion</Button>
      <br/>
      <Animate
        transitionName={state.transitionName}
        showProp="visible"
        onLeave={this.onAnimateLeave}
        component=""
        transitionAppear
      >
        <Box visible={state.visible}></Box>
      </Animate>
    </div>
  }
}
