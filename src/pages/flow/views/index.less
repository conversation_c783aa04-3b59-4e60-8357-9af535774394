.flow{
  background-color: #ffffff;
  .flow-banner {
    width: 750px;
    height: 200px;
    background: url('~src/assets/flow/flow.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .flow-content{
    .flow-content-li{
      width: 750px;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .flow-li-title{
        font-size: 32px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #000000;
        margin: 40px 0 50px 40px;
      }
      .flow-list{
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        .flow-every-ele{
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 0 40px 40px 40px;
          .ele-icon{
            width: 80px;
            height: 80px;
            margin-bottom: 14px;
          }
          span{
            font-size: 30px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
          }
        }
      }
    }
  }
}