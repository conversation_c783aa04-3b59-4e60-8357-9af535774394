/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-25 10:00:40
 * @LastEditTime: 2020-11-25 20:30:00
*/

import { getRequestsByRoot } from 'axios-service'
import srcConfig from 'src/config/apiRoots'

const { get, post, postXForm, restFulDelete } = getRequestsByRoot({ root: srcConfig.familyWorkFlow })
class Apis {
  /**
   * 流程发起-表单控件
   */
  getMyFlow = get('workflow/api/v1/workflow/user/taskList', {}, { autoLoading: false })

  /**
   * 流程审批
   * http://yapi.inkept.cn/project/607/interface/api/6514
   */
  flowApprove = post('workflow/api/v1/workflow/task/approve', {}, { autoLoading: false })

  /**
   * 待审批
   * http://yapi.inkept.cn/project/607/interface/api/6420
   */
  getPendingApproveList = get('workflow/api/v1/workflow/user/approvalPendingList', {}, { autoLoading: false })

  /**
   * 已审批
   * http://yapi.inkept.cn/project/607/interface/api/6420
   */
  getApprovedList = get('workflow/api/v1/workflow/user/approved/list', {}, { autoLoading: false })

  /**
   根据任务ID获取任务详情
   */
  getTaskDetail = get('workflow/api/v1/workflow/task', {}, { autoLoading: false })

  /**
   * @description: 获取登录用户
   * @param {*}
   * @return {*}
   */
   getLoginUser = get('workflow/api/v1/organization/user', {}, { autoLoading: false })

  /**
   * 获取全部在职人员
   */
  getAllPeople = get('workflow/api/v1/organization/onboardUsers', {}, { autoLoading: false })

  /**
   * 加签审批人
   * http://yapi.inkept.cn/project/607/interface/api/7601
   */
  counterSignApproval = post('workflow/api/v1/workflow/task/countersign', {}, { autoLoading: false })

  /**
   * 转签审批人
   * https://yapi.inkept.cn/project/607/interface/api/18884
   */
  transferApproval = post('workflow/api/v1/workflow/task/transfer/countersign', {}, { autoLoading: false })

  /**
   * http://yapi.inkept.cn/project/607/interface/api/8946
   * 流程取回/减签
   */
  flowRetrieve = post('workflow/api/v1/workflow/task/takeback', {}, { autoLoading: false })
}

export default new Apis()
