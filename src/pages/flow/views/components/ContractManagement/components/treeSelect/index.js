import React, { useState, useEffect } from 'react'
import util from 'utils'
import './index.less'

const TreeSelect = (props) => {
  const [sourceData, setSourceData] = useState(props.treeData)
  const [showSelect, setShowSelect] = useState(false)
  const [saveAllData, setSaveAllData] = useState([])
  const [inputValue, setInputValue] = useState('')
  const [saveSelected, setSaveSelected] = useState([])
  const [selectDom, setSelectDom] = useState(null)
  function focusFun () {
    setShowSelect(true)
  }

  window.document.onclick = function (e) {
    const cDom = document.getElementsByClassName('tree-select-wrapper')[0]
    if (cDom && !cDom.contains(e.target)) {
      setShowSelect(false)
    }
  }

  useEffect(() => {
    setSourceData(props.treeData)
    setSaveAllData(props.treeData)
  }, [props])

  function bodyScroll (event) {
    event.preventDefault()
  }

  function filterData (array, fn) {
    return array.reduce((r, o) => {
      var children = filterData(o.children || [], fn);
      if (fn(o) || children.length) r.push(Object.assign({}, o, children.length && { children }))
      return r;
    }, [])
  }

  function foldFun (index, e) {
    if (document.getElementById(`tree-switcher-icon${index}`).className.indexOf('tree-switcher-icon-close') !== -1) {
      document.getElementById(`tree-switcher-icon${index}`).classList.remove('tree-switcher-icon-close')
      document.getElementById(`tree-switcher-icon${index}`).classList.add('tree-switcher-icon-open')
      document.getElementById(`item-row-children${index}`).classList.remove('hide')
    } else {
      document.getElementById(`tree-switcher-icon${index}`).classList.remove('tree-switcher-icon-open')
      document.getElementById(`tree-switcher-icon${index}`).classList.add('tree-switcher-icon-close')
      document.getElementById(`item-row-children${index}`).classList.add('hide')
    }
  }

  function changeFun (e) {
    const tempData = filterData(saveAllData, ({ title }) => title.indexOf(e.target.value) !== -1)
    setInputValue(e.target.value)
    util.throttle(() => {
      setSourceData(tempData)
    }, 300, {
      leading: false
    })()
  }

  useEffect(() => {
    function deleteSelect (keyId, e) {
      const tempArr = saveSelected.filter(item => item.key !== keyId)
      setSaveSelected(tempArr)
      props.getSelectDeptFun({
        data: tempArr,
        index: props.index,
        id: props.indexId
      })
      e.stopPropagation()
    }
    function domItem (data = []) {
      return <>
        {
          data.map((item, i) => {
            return <li key={item.key} className='tree-select-li show-select-style'>
              <label>{item.title}</label>
              <label className='tree-select-li-close' onClick={deleteSelect.bind(this, item.key)}>
                <svg viewBox="64 64 896 896" focusable="false" data-icon="close" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                  <path d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 0 0 203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"></path>
                </svg>
              </label>
            </li>
          })
        }
      </>
    }
    const addDom = domItem(saveSelected)
    setSelectDom(addDom)
  }, [saveSelected, props])

  function getItemData (data) {
    setSaveSelected([...saveSelected, {
      title: data.title,
      key: data.key,
      org_id: data.org_id
    }])
    props.getSelectDeptFun({
      data: [...saveSelected, {
        title: data.title,
        key: data.key,
        org_id: data.org_id
      }],
      index: props.index,
      id: props.indexId
    })
    // document.getElementsByClassName('input-style')[0].focus()
  }

  function renderDom (sourceData) {
    return <>
      {
        sourceData.map((item, i) => {
          return <div key={i} className='item-row'>
            <span className='arrow-wrapper' id={`arrow-wrapper${item.key}`} onClick={foldFun.bind(this, item.key)}>
              <span role="img" aria-label="caret-down" id={`tree-switcher-icon${item.key}`} className={item.children && item.children.length ? 'tree-switcher-icon-open' : 'tree-switcher-icon-close hide'}>
                <svg viewBox="0 0 1024 1024" focusable="false" data-icon="caret-down" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                  <path d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"></path>
                </svg>
              </span>
            </span>
            <span className='title-style' data-data={item} onClick={getItemData.bind(this, item)}>{item.title}</span>
            {
              item.children ? <div className='item-row-children' id={`item-row-children${item.key}`}>
                {
                  renderDom(item.children)
                }
              </div> : ''
            }
          </div>
        })
      }
    </>
  }

  function getFocusFun () {
    document.getElementsByClassName('input-style')[0].focus()
    setShowSelect(true)
    // console.log(document.body.scrollHeight)
  }

  return <div className='tree-select-wrapper'>
    <ul className='tree-select-ul' onClick={getFocusFun}>
      {selectDom}
      <li className='tree-select-li' id='input-dom'>
        <input
          className='input-style'
          style={props.style}
          value={inputValue}
          // onFocus={focusFun}
          onChange={changeFun}
        ></input>
      </li>
    </ul>
    <div className={ showSelect ? 'option-wrappe' : 'hide'}>
      {
        sourceData.length ? renderDom(sourceData) : ''
      }
    </div>
  </div>
}
export default TreeSelect
