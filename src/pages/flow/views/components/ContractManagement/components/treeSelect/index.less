.tree-select-wrapper {
  .hide {
    display: none !important;
  }
  .input-style {
    padding-left: 15px;
    font-size: 34px;
    height: 100%;
  }
  .input-style::placeholder {
    color: #bfbfbf;
    font-size: 34px;
  }
  .option-wrappe {
    position: absolute;
    max-height: 400px;
    min-height: 200px;
    overflow: auto;
    box-shadow: 0 3px 6px -4px rgba(0,0,0,.12), 0 6px 16px 0 rgba(0,0,0,.08), 0 9px 28px 8px rgba(0,0,0,.05);
    background-color: #fff;
    border-radius: 2px;
    outline: none;
    top: 88px;
    z-index: 200;
  }
  .title-style {
    display: inline-block;
    font-size: 14px;
    margin-top: 12px;
  }
  .item-row {
    margin-left: 25px;
    // padding-bottom: 8px;
    font-size: 34px;
    .tree-switcher-icon-close svg {
      transition: transform .3s;
      transform: rotate(-90deg);
      position: relative;
      top: 4px;
    }
    .tree-switcher-icon-open svg {
      transition: transform .3s;
      position: relative;
      transform: rotate(0deg);
      top: 4px;
    }
    .arrow-wrapper {
      display: inline-block;
      width: 45px;
    }
  }
  .tree-select-ul {
    position: relative;
    // border: 1px solid #f0f0f0;
    height: 90px;
    line-height: 90px;
    display: flex;
    flex-wrap: wrap;
    overflow: auto;
    top: 10px;
  }
  .tree-select-li {
    position: static;
    // float: left;
    width: auto;
    max-width: 100%;
    padding: 0;
    font-size: 34px;
  }
  .show-select-style {
    color: rgba(0,0,0,.65);
    background-color: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 2px;
    padding: 0 20px 0 10px;
    font-size: 14px;
    height: 60px;
    line-height: 55px;
    // display: flex;
    // align-items: center;
    margin: 10px 10px 0 0;
  }
  .tree-select-li-close {
    position: relative;
    left: 10px;
  }
}