import React, { useState, useEffect, useImperative<PERSON>andle, forwardRef } from 'react'
import { Button } from 'antd-mobile'
import { Upload } from 'antd'
import './index.less'
const serverURL = `https://upload.inke.cn/upload/media?sufix=`;

const UploadFile = (props) => {
  const [uploadUrl, setUploadUrl] = useState('')
  const [attachment, setAttachment] = useState([])
  function beforeFileUpload (file) {
    let str = file.name
    setUploadUrl(serverURL + str.slice(str.lastIndexOf('.') + 1, str.length))
    return true
  }

  function handleUploadChange (info) {
    if (info.file.status === 'done') {
      console.log(info)
      setAttachment([{
        fileUrl: info.file.response.url,
        fileName: info.file.name,
        type: 'task'
      }])
    }
  }

  useEffect(() => {
    props.getUpload(attachment)
  }, [attachment, props])

  return <div className='upload-wrapper'>
    <Upload
      name="logo"
      listType="text"
      action={uploadUrl}
      onChange={handleUploadChange}
      beforeUpload={beforeFileUpload}
      // customRequest={customRequest}
      showUploadList={false}
    >
      <Button className='new-flow-upload'>
        <span className='new-flow-upload-text'>上传附件</span>
      </Button>
    </Upload>
  </div>
}
export default UploadFile
