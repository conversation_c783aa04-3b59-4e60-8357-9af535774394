import React, { useState, useEffect, useImperativeHandle, forwardRef, memo } from 'react'
import { TextareaItem } from 'antd-mobile'

const Mark = (props) => {
  const [inputValue, setInputValue] = useState('')

  function inputChange (value) {
    setInputValue(value)
    props.getMark(value.trim())
  }

  return <div>
    <TextareaItem
      title="备注"
      placeholder="请输入(必填项)"
      data-seed="logId"
      autoHeight
      value={inputValue}
      onChange={inputChange}
    />
  </div>
}
export default memo(Mark)
