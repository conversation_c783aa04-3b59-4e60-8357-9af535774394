.select-wrapper {
  // position: relative;
  .select-ul {
    position: absolute;
    background: #fff;
    max-height: 300px;
    margin-bottom: 0;
    padding: 4px 0;
    padding-left: 0;
    overflow: auto;
    list-style: none;
    outline: none;
    box-shadow: 0 2px 8px rgba(0,0,0,.15);
    z-index: 100;
    top: 80px;
    .select-item {
      position: relative;
      display: block;
      padding: 25px 20px;
      overflow: hidden;
      color: rgba(0,0,0,.65);
      font-weight: 400;
      font-size: 30px;
      line-height: 22px;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .select-item-no-data {
      font-size: 30px;
      text-align: center;
      padding: 50px 0;
      color: #bbb;
    }
  }
  .hide {
    display: none;
  }
}
