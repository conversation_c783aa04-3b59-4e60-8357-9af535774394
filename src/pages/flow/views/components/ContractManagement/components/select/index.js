import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react'
import util from 'utils'
import './index.less'
let allData = []

const Select = (props = {}, ref) => {
  const [showSelect, setShowSelect] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const [selectData, setSelectData] = useState([])
  const [currentIndex, setCurrentIndex] = useState('')

  function focusFun () {
    setShowSelect(true)
  }

  useEffect(() => {
    if (props.data.length) {
      setSelectData(props.data)
      allData = props.data
      setCurrentIndex(props.index)
    }
  }, [props])

  // useImperativeHandle(ref, () => ({
  //   // sendSelectData 就是暴露给父组件的方法
  //   sendSelectData: () => {
  //     return {
  //       index: currentIndex,
  //       data: inputValue
  //     }
  //   }
  // }))

  function blurFun () {
    setTimeout(() => {
      setShowSelect(false)
    }, 100);
  }

  function selectItemFun (data) {
    setInputValue(data.option_name)
    props.getSelectData({
      index: props.index,
      id: props.indexId,
      selectData: data
    })
  }

  function searchInputFun (e) {
    setInputValue(e.target.value)
    const tempArr = allData.filter(item => item.option_name.indexOf(e.target.value) !== -1)
    util.throttle(() => {
      setSelectData(tempArr)
    }, 300, {
      leading: false
    })()
  }

  return <div className='select-wrapper'>
    <input style={{ width: 200 }} value={inputValue} placeholder='请选择' onFocus={focusFun} onBlur={blurFun} onChange={searchInputFun}></input>
    <ul className={ showSelect ? 'select-ul' : 'select-ul hide'} style={{ width: 200 }}>
      {
        selectData.map((item, i) => {
          return <li className='select-item' key={i} onClick={selectItemFun.bind(this, item)}>{item.option_name}</li>
        })
      }
      {
        selectData.length ? '' : <li className='select-item-no-data'>无数据</li>
      }
    </ul>
  </div>
}
export default forwardRef(Select)
