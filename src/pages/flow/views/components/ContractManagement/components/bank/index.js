import React, { useState, useEffect, useImperative<PERSON>andle, forwardRef } from 'react'
import { List, InputItem, Toast } from 'antd-mobile';

const Bank = (props) => {
  const [showData, setShowData] = useState([])
  const [bankInputData, setBankInputData] = useState({
    key0: '',
    key1: ''
  })

  useEffect(() => {
    if (props.dataSource.length) {
      setShowData(props.dataSource)
    }
  }, [props])

  function inputChange (index, e) {
    const tempObj = Object.assign({}, {
      ...bankInputData,
      [`key${index}`]: e
    })
    setBankInputData(tempObj)
    props.getBankData({
      data: tempObj,
      index: props.index,
      id: props.indexId
    })
  }

  function renderDom (obj, index) {
    if (obj.parts_type === 'input') {
      return <InputItem
        clear
        placeholder="请输入"
        value={bankInputData[`key${index}`]}
        disabled={obj.parts_type === 'text'}
        onChange={inputChange.bind(this, index)}
      >{obj.field_name}</InputItem>
    }
  }

  return <div>
    {
      showData.map((item, i) => {
        return <List key={i}>
          {
            renderDom(item, i)
          }
        </List>
      })
    }
  </div>
}
export default Bank
