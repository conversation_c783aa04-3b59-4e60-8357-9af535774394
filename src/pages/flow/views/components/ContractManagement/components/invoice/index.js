import React, { useState, useEffect, useImperative<PERSON><PERSON><PERSON>, forwardRef, Component } from 'react'
import { List, InputItem, Toast } from 'antd-mobile';
import { createForm } from 'rc-form';
import Select from '../select'
import API from './apis'
import './index.less'
import Confirm from 'components/confirm'
class Invoice extends Component {
  constructor(props) {
    super(props)
    this.state = {
      invoiceData: [],
      allData: [],
      keyData: {
        key0: '',
        key1: '',
        key2: '',
        key3: '',
        key4: ''
      }
    }
  }

  componentDidMount () {
    this.getInvoiceList()
    this.setState({
      allData: this.props.dataSource
    })
  }

  getInvoiceList = () => {
    API.getInvoiceList().then(res => {
      const tempArr = []
      res.data && res.data.rate.forEach(item => {
        tempArr.push({
          option_name: item.name,
          option_value: item.rate
        })
      })
      this.setState({
        invoiceData: tempArr
      })
    }, err => {
      // Toast.fail(err.msg)
      Confirm.ToastMessage(err.msg)
    })
  }

  getSelectData = (data) => {
    const tempObj = Object.assign({}, { ...this.state.keyData }, { key0: data.selectData.option_name, key1: data.selectData.option_value })
    this.setState({
      keyData: tempObj
    })
  }

  toPoint = (percent) => {
    var str = percent.replace('%', '')
    str = str / 100
    return Number(str)
  }

  inputChange = (index, e) => {
    const tempObj = Object.assign({}, { ...this.state.keyData }, {
      key2: Number(e),
      key3: (Number(e) * this.toPoint(this.state.keyData.key1)).toFixed(2),
      key4: (Number(e) - Number(e) * this.toPoint(this.state.keyData.key1)).toFixed(2)
    })
    this.setState({
      keyData: tempObj
    }, () => {
      this.props.getChildInvoiceData({
        data: tempObj,
        index: this.props.index,
        id: this.props.indexId
      })
    })
  }

  renderDom = (obj, index) => {
    const { getFieldProps } = this.props.form;
    if (obj.parts_type === 'input' || obj.parts_type === 'text') {
      return <InputItem
        {...getFieldProps(`key_${index}`)}
        clear
        placeholder="请输入"
        id={`key${index}`}
        value={this.state.keyData[`key${index}`]}
        onChange={this.inputChange.bind(this, `key${index}`)}
        extra={Number(index) > 1 ? '￥' : null}
        type={Number(index) > 1 ? 'number' : 'text'}
        disabled={obj.parts_type === 'text'}
      >{obj.field_name}</InputItem>
    } else if (obj.parts_type === 'dropdown') {
      return <div className="am-list am-list-select">
        <div className="am-list-body">
          <div className="am-list-item am-input-item am-list-item-middle">
            <div className="am-list-line">
              <div className="am-input-label am-input-label-5">{obj.field_name}</div>
              <div className="am-input-control option">
                {
                  <Select data={this.state.invoiceData} index={`key_${index}`} getSelectData={this.getSelectData}></Select>
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    }
  }

  render () {
    const {
      allData
    } = this.state
    return <div>
      {
        allData.map((item, i) => {
          return <List key={i}>
            {
              this.renderDom(item, i)
            }
          </List>
        })
      }
    </div>
  }
}
export default createForm()(Invoice)
