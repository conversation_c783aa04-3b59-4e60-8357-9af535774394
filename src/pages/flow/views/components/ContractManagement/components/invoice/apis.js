/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-24 14:00:40
 * @LastEditTime: 2020-11-24 20:35:00
*/

import { getRequestsByRoot } from 'axios-service'
import srcConfig from 'src/config/apiRoots'

const { get, post, postXForm, restFulDelete } = getRequestsByRoot({ root: srcConfig.familyWorkFlow })
class Apis {
  /**
   * 获取发票类型
   */
  getInvoiceList = get('workflow/api/v1/form/rate/all', {}, { autoLoading: false })
}
export default new Apis()
