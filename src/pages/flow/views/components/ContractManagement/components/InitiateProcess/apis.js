/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-24 14:00:40
 * @LastEditTime: 2020-11-24 20:35:00
*/

import { getRequestsByRoot } from 'axios-service'
import srcConfig from 'src/config/apiRoots'

const { get, post, postXForm, restFulDelete } = getRequestsByRoot({ root: srcConfig.familyWorkFlow })
class Apis {
  /**
   * 流程发起-表单控件
   */
  getFormDetails = get('workflow/api/v1/form/detail', {}, { autoLoading: false })

  /**
   * 获取所有部门
   */
  getAllDepartment = get('workflow/api/v1/organization/departmentTree', {}, { autoLoading: false })

  /**
   * 获取发票类型
   */
  getInvoiceList = get('workflow/api/v1/form/rate/all', {}, { autoLoading: false })

  /**
   * 获取所有员工
   */
  getAllUser = get('workflow/api/v1/organization/users', {}, { autoLoading: false })

  /**
   * 新建任务
   * http://yapi.inkept.cn/project/607/interface/api/6284
   */
  newTask = post('workflow/api/v1/workflow/task', {}, { autoLoading: false })

  /**
   * 查询流程信息
   * http://yapi.inkept.cn/project/607/interface/api/6280
   */
   queryProcess = get('workflow/api/v1/workflow/process', {}, { autoLoading: false })

   /**
    * 查询流程记录信息
    * http://yapi.inkept.cn/project/607/interface/api/6285
    */
  queryProessMessage = get('workflow/api/v1/workflow/task', {}, { autoLoading: false })
}

export default new Apis()
