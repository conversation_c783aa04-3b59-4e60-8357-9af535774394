/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-11 14:05:20
 * @LastEditTime: 2021-06-01 17:15:29
 * @LastEditors: Please set LastEditors
 * @Description: 发起预约
 */
import React, { PureComponent } from 'react'
import { List, InputItem, Switch, Toast, Picker, Button, DatePicker, Checkbox, Radio, TextareaItem } from 'antd-mobile';
import { createForm } from 'rc-form';
import TreeSelect from '../treeSelect'
import Select from '../select'
import Invoice from '../invoice'
import Bank from '../bank'
// import Mark from '../mark'
import Upload from '../upload'
import './index.less'
import NavTopBar from 'components/NavTopBar'
import API from './apis'
import Confirm from 'components/confirm'
const Item = List.Item;
class ApplyAppointment extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      fieldArray: [],
      fieldType: 0,
      fieldName: '',
      fieldId: '',
      flowMessage: {},
      treeData: [],
      paramValue: new Date().format('yyyMMddhhmmss') + Math.random().toString(36).slice(-8),
      radioValue: '',
      allUsers: [],
      time: '',
      contractNumber: '',
      remark: '',
      attachment: [],
      titleName: ''
    }
    this.processObj = {}
    this.formObj = {}
  }

  componentDidMount() {
    this.initData()
    // this.SaveObject()
    // this.getAllDepartment()
    // this.getAllUser()
  }

  getAllUser = () => {
    API.getAllUser().then(res => {
      const tempArr = []
      res.data && res.data.forEach(item => {
        tempArr.push({
          option_value: item.id,
          option_name: item.name
        })
      })
      this.setState({
        allUsers: tempArr
      })
    }, err => {
      // Toast.fail(err.response.data.msg)
      Confirm.ToastMessage(err.response.data.msg)
    })
  }

  getAllDepartment = () => {
    API.getAllDepartment().then(res => {
      this.setState({
        treeData: res.data ? res.data : []
      })
    }).catch(err => {
      // Toast.fail(err.response.data.msg)
      Confirm.ToastMessage(err.response.data.msg)
    })
  }

  /**
   * 初始化数据
   */
  initData = async () => {
    let data = await this.queryProcess()
    this.processObj = data?.data
    // 如果存在表单记录id，就用此id进行回显
    let historyData = this.props.history.location
    if (historyData.state?.formRecordId) {
      this.queryProessMessage(historyData.state?.formRecordId)
    } else {
      this.getFormDetails(historyData.state?.formId)
      this.setState({
        titleName: historyData.state?.name + '-' + this.processObj.code
      })
    }
  }

  queryProcess = () => {
    return API.queryProcess({
      pid: this.props.history.location.state?.processId
    }).catch(err => {
      Confirm.ToastMessage(err.response.data.msg)
    })
  }

  queryProessMessage = (id) => {
    API.queryProessMessage({
      id: id
    }).then(res => {
      this.setState({
        titleName: res.data?.form.name
      })
      let tempArr = res.data?.form?.fields
      tempArr.forEach(item => {
        item.randomId = this.randomString()
        // if (item.parts_type === 'foreign') {
        //   // 请求外部关联字段
        //   fetchForeignFormRecords(item)
        // }
      })
      const employeesIndex = tempArr.findIndex(item => item.parts_type === 'employees')
      const deptIndex = tempArr.findIndex(item => item.parts_type === 'dept')
      // 含有员工字段去请求员工接口
      if (employeesIndex !== -1) {
        this.getAllUser()
      }
      // 含有部门字段去请求部门接口
      if (deptIndex !== -1) {
        this.getAllDepartment()
      }
      this.setState({
        fieldArray: tempArr
      })
      // setFieldsArray(tempArr)
      // setAttachment(res.data.form.attachment ? res.data.form.attachment : [])
      // tempArr.forEach(item => {
      //   form.setFieldsValue({
      //     [`item${item.randomId}`]: item.value
      //   })
      // })
      this.formObj = res.data?.form
      // 状态只要不是5草稿箱，就要重新赋值给process
      if (res.data?.process.status === 5) {
        this.processObj = res.data?.process
      }
    }).catch(err => {
      Confirm.ToastMessage(err.response.data.msg)
    })
  }

  // 生成随机数
  randomString = (e) => {
    e = e || 32;
    var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
    let a = t.length
    let n = ''
    for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
    return n
  }

  /**
   * 获取表单信息
   */
  getFormDetails = (formId) => {
    API.getFormDetails({
      id: formId
    }).then(res => {
      let tempArr = res.data?.fields
      tempArr.forEach(item => {
        item.randomId = this.randomString()
        // if (item.parts_type === 'foreign') {
        //   // 请求外部关联字段
        //   fetchForeignFormRecords(item)
        // }
      })
      const employeesIndex = tempArr.findIndex(item => item.parts_type === 'employees')
      const deptIndex = tempArr.findIndex(item => item.parts_type === 'dept')
      // 含有员工字段去请求员工接口
      if (employeesIndex !== -1) {
        this.getAllUser()
      }
      // 含有部门字段去请求部门接口
      if (deptIndex !== -1) {
        this.getAllDepartment()
      }
      this.setState({
        fieldArray: tempArr
      })
      this.formObj = res.data
    }, err => {
      Confirm.ToastMessage(err.response.data.msg)
    })
  }

  // SaveObject = () => {
  //   if (this.props.location.query) {
  //     window.sessionStorage.setItem('keys', JSON.stringify(this.props.location.query.details))
  //   }
  //   const result = JSON.parse(sessionStorage.getItem('keys'))
  //   if (result && result.process) {
  //     this.setState({
  //       paramValue: new Date().format('yyyMMddhhmmss') + Math.random().toString(36).slice(-8),
  //     })
  //   } else {
  //     this.setState({
  //       paramValue: new Date().format('yyyMMddhhmmss') + Math.random().toString(36).slice(-8),
  //       flowMessage: result
  //     }, () => {
  //       this.getFormDetails()
  //     })
  //   }
  // }

  /**
   * 获取表单控件
   */
  // getFormDetails = () => {
  //   API.getFormDetails({
  //     id: this.state.flowMessage.formId
  //   }).then(res => {
  //   // console.log('数据', res)
  //     res.data.fields.forEach(item => {
  //       if (item.parts_type === 'invoice') {
  //         // 获取所有税率
  //         // this.getAllRate()
  //       }
  //       if (item.parts_type === 'supplier') {
  //         // 获取供应商
  //         // this.getSupplierList()
  //       }
  //     })
  //     this.setState({
  //       fieldArray: res.data.fields ? res.data.fields : [],
  //       fieldType: res.data.type,
  //       fieldName: res.data.name,
  //       fieldId: res.data.id
  //     })
  //   }).catch(err => {
  //     console.log(err)
  //   })
  // }

  /**
   * 新建任务
   */
  newTask = () => {
    const obj = JSON.parse(sessionStorage.getItem('keys'))
    delete obj.createdAt
    delete obj.updatedAt
    obj.template_id = obj.id
    delete obj.id
    API.newTask({
      process: obj,
      form: {
        name: this.state.fieldName.split('_')[0] + '_' + this.state.contractNumber,
        template_id: this.state.flowMessage.form ? this.state.flowMessage.form.template_id : obj.formId,
        type: this.state.fieldType,
        fields: this.state.fieldArray,
        contractNumber: this.state.contractNumber,
        attachment: this.state.attachment,
        remark: this.state.remark
      }
    }).then(res => {
      // Toast.success('发起成功')
      Confirm.ToastMessage('发起成功')
      this.props.history.push({ pathname: '/contract/' })
    }).catch(err => {
      // Toast.fail(err.response.data.msg)
      Confirm.ToastMessage(err.response.data.msg)
    })
  }

  /**
   * 提交表单
   */
  onSubmit = () => {
    this.props.form.validateFields((err, values) => {
      if (err || !this.state.remark) {
        // Toast.info('必填项不能为空', 2);
        Confirm.ToastMessage('必填项不能为空')
        return
      }
      console.log('form数据', values)
      const tempArr = this.state.fieldArray
      const checkboxArr = []
      Object.keys(values).forEach(item => {
        if (item.indexOf('key_checkbox') !== -1 && values[item]) {
          const arr = item.split('_')
          tempArr.forEach(item => {
            if (item.id === arr[2]) {
              checkboxArr.push(arr[3])
              item.value = checkboxArr
            }
          })
        }
        if (item.indexOf('key_date') !== -1 && values[item]) {
          const arr = item.split('_')
          tempArr.forEach(list => {
            if (list.id === arr[2]) {
              list.value = this.gmtToStr(values[item])
            }
          })
        }
      })
      this.setState({
        fieldArray: tempArr,
        contractNumber: values.contractNumber.toString()
      }, () => {
        console.log(tempArr)
        this.newTask()
      })
    })
  }

  /**
   * 保存草稿
   */
  onReset = () => {
    // this.props.form.resetFields();
  }

  radioChange = (value, id) => {
    const tempArr = this.state.fieldArray
    tempArr.forEach(item => {
      if (item.id === id) {
        item.value = value
      }
    })
    this.setState({
      radioValue: value,
      fieldArray: tempArr
    })
  }

  getSelectData = (data) => {
    const tempArr = this.state.fieldArray
    tempArr.forEach((item, i) => {
      if (item.id === data.id) {
        item.value = data.selectData.option_value
      }
    })
    this.setState({
      fieldArray: tempArr
    }, () => {
      console.log(this.state.fieldArray)
    })
  }

  getSelectDeptFun = (data) => {
    const tempArr = this.state.fieldArray
    tempArr.forEach(item => {
      if (item.id === data.id) {
        const arr = []
        data.data.forEach(list => {
          arr.push(list.title)
        })
        item.value = arr.toString()
      }
    })
    this.setState({
      fieldArray: tempArr
    })
  }

  getChildInvoiceData = (data, index) => {
    const tempArr = this.state.fieldArray
    tempArr.forEach(item => {
      if (item.id === data.id) {
        item.fields.forEach((list, i) => {
          list.value = data.data[`key${i}`]
        })
      }
    })
    this.setState({
      fieldArray: tempArr
    })
  }

  getBankData = (data) => {
    const tempArr = this.state.fieldArray
    tempArr.forEach(item => {
      if (item.id === data.id) {
        item.fields.forEach((list, i) => {
          list.value = data.data[`key${i}`]
        })
      }
    })
    this.setState({
      fieldArray: tempArr
    })
  }

  changInput = (id, value) => {
    const tempArr = this.state.fieldArray
    tempArr.forEach(item => {
      if (item.id === id) {
        item.value = value
      }
    })
    this.setState({
      fieldArray: tempArr
    })
  }

  // getMarkFun = (value) => {
  //   this.setState({
  //     remark: value
  //   })
  // }

  inputChange (value) {
    console.log(value)
    // this.setState({
    //   remark: value
    // })
  }

  getUploadFile = (arr) => {
    this.setState({
      attachment: arr
    })
  }

  gmtToStr = (time) => {
    let date = new Date(time)
    let Str = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
    return Str
  }

  renderDom = (obj, index) => {
    const { getFieldProps } = this.props.form;
    // console.log('类型', obj)
    if (obj.parts_type === 'input') {
      return <InputItem
        {...getFieldProps(`key_${index}`)}
        clear
        placeholder="请输入"
        onChange={this.changInput.bind(this, obj.id)}
        labelNumber={8}
      >{obj.field_name}</InputItem>
    } else if (obj.parts_type === 'dropdown') {
      return <div className="am-list am-list-select">
        <div className="am-list-body">
          <div className="am-list-item am-input-item am-list-item-middle">
            <div className="am-list-line">
              <div className="am-input-label am-input-label-5">{obj.field_name}</div>
              <div className="am-input-control option">
                {
                  <Select data={obj.parts_condition} indexId={obj.id} index={`key_${index}`} getSelectData={this.getSelectData}></Select>
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    } else if (obj.parts_type === 'date') {
      return <div className='date-item'>
        <DatePicker
          mode="date"
          extra="选择日期"
          format='YYYY-MM-DD'
          {...getFieldProps(`key_date_${obj.id}`)}
          // onChange={this.dateChange.bind(this, obj.id)}
        >
          <List.Item>{obj.field_name}</List.Item>
        </DatePicker>
      </div>
    } else if (obj.parts_type === 'checkbox') {
      return <div className="am-list">
        <div className="am-list-body am-list-checkbox">
          <div className="am-list-item am-input-item am-list-item-middle">
            <div className="am-list-line">
              <div className="am-input-label am-input-label-5">{obj.field_name}</div>
              <div className="am-input-control option">
                {
                  obj.parts_condition.map((item, i) => {
                    return <Checkbox.CheckboxItem {...getFieldProps(`key_checkbox_${obj.id}_${item.option_value}_${index}`)} key={i}>{item.option_name}</Checkbox.CheckboxItem>
                  })
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    } else if (obj.parts_type === 'radio') {
      return <div className="am-list am-list-radio-wrapper">
        <div className="am-list-body">
          <div className="am-list-item am-input-item am-list-item-middle">
            <div className="am-list-line">
              <div className="am-input-label am-input-label-5">{obj.field_name}</div>
              <div className="am-input-control option">
                {
                  obj.parts_condition.map((item, i) => {
                    return <Radio.RadioItem key={i} checked={this.state.radioValue === item.option_value} onChange={this.radioChange.bind(this, item.option_value, obj.id)}>{item.option_name}</Radio.RadioItem>
                  })
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    } else if (obj.parts_type === 'invoice') {
      return <div>
        <div className='item-wrapper-title'>{obj.field_name}</div>
        <Invoice index={`key${index}`} indexId={obj.id} dataSource={obj.fields} getChildInvoiceData={this.getChildInvoiceData}></Invoice>
      </div>
    } else if (obj.parts_type === 'dept') {
      return <div className="am-list am-list-select">
        <div className="am-list-body">
          <div className="am-list-item am-input-item am-list-item-middle">
            <div className="am-list-line">
              <div className="am-input-label am-input-label-5">{obj.field_name}</div>
              <div className="am-input-control option">
                <TreeSelect
                  style={{
                    width: '100%',
                  }}
                  treeData={this.state.treeData}
                  getSelectDeptFun={this.getSelectDeptFun}
                  indexId={obj.id}
                  index={`key${index}`}
                ></TreeSelect>
              </div>
            </div>
          </div>
        </div>
      </div>
    } else if (obj.parts_type === 'employees') {
      return <div className="am-list am-list-select">
        <div className="am-list-body">
          <div className="am-list-item am-input-item am-list-item-middle">
            <div className="am-list-line">
              <div className="am-input-label am-input-label-5">{obj.field_name}</div>
              <div className="am-input-control option">
                <Select data={this.state.allUsers} indexId={obj.id} index={`key_${index}`} getSelectData={this.getSelectData}></Select>
              </div>
            </div>
          </div>
        </div>
      </div>
    } else if (obj.parts_type === 'repeat') {
      return <div>
        <div className='item-wrapper-title'>{obj.field_name}</div>
        {/* {
          obj.fields.map((item, i) => {
            return <div key={i}>
              {
                this.renderDom(item, i, index)
              }
            </div>
          })
        } */}
        <Bank dataSource={obj.fields} indexId={obj.id} index={`key${index}`} getBankData={this.getBankData}></Bank>
      </div>
    }
  }

  render() {
    const { getFieldProps, getFieldError, getFieldDecorator } = this.props.form;
    const {
      fieldArray,
      paramValue,
      treeData
    } = this.state
    return <div className='initiate-process'>
      <div className='nav-bar'>
        {/* <NavTopBar title='流程发起' backRouter={true} {...this.props} /> */}
      </div>
      <div className='initiate-process-form'>
        <form>
          <List
            className='process-content'
          >
            <InputItem
              disabled
              {...getFieldProps('contractNumber', {
                initialValue: paramValue
              })}
              placeholder="请输入"
              className='item-list have-star'
            >
              流程编号：
            </InputItem>
          </List>
          {
            fieldArray.map((item, i) => {
              return <List key={i}>
                {
                  this.renderDom(item, i)
                }
              </List>
            })
          }
          <List>
            {/* <Mark getMark={this.getMarkFun}></Mark> */}
            <TextareaItem
              title="备注"
              placeholder="请输入(必填项)"
              data-seed="logId"
              autoHeight
              value={this.state.remark}
              onChange={this.inputChange}
            />
          </List>
          <List>
            <div className="am-list">
              <div className="am-list-body am-list-checkbox">
                <div className="am-list-item am-input-item am-list-item-middle">
                  <div className="am-list-line">
                    <div className="am-input-label">附件</div>
                    <div className="am-input-control option">
                      <Upload getUpload={this.getUploadFile}></Upload>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </List>
        </form>
      </div>
      <div className='form-btn'>
        <Button
          size="small"
          inline
          onClick={this.onReset}
          className='btn-left'
        >
          保存草稿
        </Button>
        <Button
          size="small"
          inline
          onClick={this.onSubmit}
          className='btn-right'
        >
          确认
        </Button>
      </div>
    </div>
  }
}

export default createForm()(ApplyAppointment)
