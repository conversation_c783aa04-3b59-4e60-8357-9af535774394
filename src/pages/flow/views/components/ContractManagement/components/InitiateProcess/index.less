.initiate-process{
  display: flex;
  flex-direction: column;
  height: 100vh;
  // .am-input-label {
  //   width: 236px;
  //   text-align: left;
  // }
  .nav-bar{
    flex: 0 0 auto;
  }
  .initiate-process-form{
    flex: 1 0 auto;
    .process-content{
      margin-top: 20px;
      box-shadow: 0 -20px 0 0 #EEF3F3;
    }
    .item-list{
      height: 118px !important;
      font-size: 34px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #000000;
      padding: 0 !important;
      .am-list-line{
        padding: 0 40px !important;
      }
    }
    .have-star{
      background: url('~src/assets/visitor/star.png') no-repeat;
      background-size: 12px 12px;
      background-position: 23px center;
    }
    .am-list-item .am-input-control {
      padding-left: 20px;
    }
  }
  .form-btn{
    flex: 0 0 auto;
    width: 750px;
    height: 120px;
    background: #FFFFFF;
    box-shadow: 0px -4px 12px 0px rgba(0, 0, 0, 0.06);
    display: flex;
    justify-content: space-around;
    align-items: center;
    .btn-left{
      width: 306px;
      height: 88px;
      background: #F1F1F1;
      border-radius: 10px;
      color: #333333;
      line-height: 88px;
      font-size: 30px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
    }
    .btn-right{
      width: 306px;
      height: 88px;
      background: #00C8B7;
      border-radius: 10px;
      color: #FFFFFF;
      line-height: 88px;
      font-size: 30px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
    }
  }
  .am-list-item .am-input-label {
    text-align: right;
    width: 170px;
  }
  .am-textarea-label {
    text-align: right;
    width: 170px;
  }
  .am-list-body::before{
    height: 0px !important;
  }
  .ant-row{
    margin: 0;
  }
  .item-wrapper-title {
    text-align: center;
    padding-top: 40px;
    color: #000;
    font-size: 30px;
    font-weight: inherit;
    padding-bottom: 5px;
  }
  .checkbox-form{
    padding: 0 40px;
  }
  .am-list-checkbox {
    .am-list-item.am-input-item {
      height: auto;
    }
    .am-list-line {
      overflow: visible;
    }
  }
  .date-item {
    .am-list-content {
      flex: none;
    }
  }
  .am-list-radio-wrapper {
    .am-list-line {
      overflow: visible;
    }
    .am-list-item.am-input-item {
      height: auto;
    }
  }
  .am-list-select {
    .am-list-item {
      position: static;
    }
    .am-list-line {
      position: static;
    }
  }
}
