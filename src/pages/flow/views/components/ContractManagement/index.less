.contract-firstpage{
  .contract-refresh{
    height: calc(100vh - 108px - 88px);
    overflow: auto;
    .contract-firstpage-ul{
      overflow: hidden;
      position: relative;
      margin-bottom: 0;
      .contract-firstpage-li{
        width: 750px;
        height: 237px;
        box-shadow: 0px 1px 0px 0px #E8E8E8;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 0 40px;
      }
      .content-left{
        display: flex;
        flex-direction: column;
        font-family: PingFangSC-Medium, PingFang SC;
        font-size: 26px;
        font-weight: 400;
        color: #999999;
        line-height: 40px;
        span:nth-child(1) {
          font-size: 34px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #000000;
          line-height: 56px;
        }
      }
      .content-right{
        width: 126px;
        height: 60px;
        background: linear-gradient(270deg, #00C8B7 0%, #00C8B7 100%);
        border-radius: 10px;
        font-size: 30px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
        text-align: center;
        line-height: 60px;
      }
    }
  }
  .am-pull-to-refresh-indicator{
    line-height: 110px;
  }
}
.am-pull-to-refresh-content-wrapper{
  min-height: 100%;
}