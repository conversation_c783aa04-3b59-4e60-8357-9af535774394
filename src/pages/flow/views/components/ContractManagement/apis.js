/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-23 16:00:40
 * @LastEditTime: 2020-11-23 20:35:00
*/

import { getRequestsByRoot } from 'axios-service'
import srcConfig from 'src/config/apiRoots'
console.log(srcConfig.familyWorkFlow)
const { get } = getRequestsByRoot({ root: srcConfig.familyWorkFlow })
class Apis {
  /**
   * 流程列表
   */
  getFlowList = get('workflow/api/v1/workflow/processList', {}, { autoLoading: false })

  /**
   * 获取流程发起-详情数据
   */
  getDetails = get('workflow/api/v1/form/detail', {}, { autoLoading: false })
}

export default new Apis()
