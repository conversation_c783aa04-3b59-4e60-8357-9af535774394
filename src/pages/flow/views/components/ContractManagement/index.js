/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-03 14:27:28
 * @LastEditTime: 2020-11-20 20:28:47
 * @LastEditors: Please set LastEditors
 * @Description: 订餐
 */
import React, { PureComponent } from 'react'
import ReactDOM from 'react-dom'
import { PullToRefresh } from 'antd-mobile';
import './index.less'
import API from './apis'
import NavTopBar from 'components/NavTopBar'

class Order extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      searchParams: {
        page: 1,
        page_size: 50
      },
      NowPage: 1,
      total: 0,
      IsGoReq: 0,
      refreshing: false,
      flowList: [],
      damping: 60,
      flag: false,
      urlName: window.location.origin + window.location.pathname
    };
  }

  componentDidMount() {
    this.getFlowList()
  }

  getFlowList = (flag) => {
    const { searchParams, flowList } = this.state
    API.getFlowList(searchParams).then(res => {
      const isTrue = res.data.process ? res.data.total - (res.data.page * searchParams.page_size) : 0
      if (isTrue > 0) {
        this.setState({
          damping: 60
        })
      } else {
        this.setState({
          damping: 50,
          flag: true
        })
      }
      if (flag) {
        this.setState({
          flowList: flowList.concat(res.data.process),
          NowPage: res.data.page,
          total: res.data.total,
          IsGoReq: isTrue,
          refreshing: false,
        })
      } else {
        this.setState({
          flowList: res.data.process || [],
          NowPage: res.data.page,
          total: res.data.total,
          IsGoReq: isTrue,
          refreshing: false,
        }, () => {
          const myComp = this.refs.RefContractFirst
          const dom = ReactDOM.findDOMNode(myComp)
          if (dom) {
            if (dom.clientHeight < document.querySelector('.contract-refresh').scrollHeight) {
              this.setState({
                damping: -10
              })
              document.querySelector('.contract-refresh .am-pull-to-refresh-indicator').style.display = 'none'
            }
          }
        })
      }
    }).catch(err => {
      console.log(err)
    })
  }

  InitiateProcess = (item) => {
    // this.props.history.push({ pathname: '/contract/' + item.id, query: { details: item } })
    this.props.history.push({
      pathname: '/contract/' + item.id,
      state: {
        processId: item.id,
        formId: item.formId,
        formRecordId: '', // 表单记录id用于回显
        status: item.status,
        name: item.name
      }
    })
  }

  // 拉动刷新
  onRefresh = () => {
    const { searchParams, NowPage, IsGoReq } = this.state
    if (IsGoReq > 0) {
      this.setState({
        searchParams: {
          ...searchParams,
          page: NowPage + 1
        }
      }, () => {
        this.getFlowList(true)
      })
    }
  }

  render () {
    const { flowList, damping } = this.state
    return (
      <div className='contract-firstpage'>
        <div className='contract-nav-bar'>
          {/* <NavTopBar title='流程列表' JumpUrl={this.state.urlName} /> */}
        </div>
        {
          flowList.length ? <PullToRefresh
            damping={damping}
            className='contract-refresh'
            direction={'up'}
            refreshing={this.state.refreshing}
            distanceToRefresh={25}
            indicator={
              this.state.flag ? { activate: '到底啦～', deactivate: '到底啦～', release: '到底啦～', finish: '到底啦～' } : {}
            }
            onRefresh={() => {
              this.onRefresh()
            }}
          >
            {
              <ul className='contract-firstpage-ul' ref='RefContractFirst'>
                {
                  flowList.length ? flowList.map((item, index) => {
                    return <li className='contract-firstpage-li' key={index}>
                      <div className='content-left'>
                        <span>{item.name}</span>
                        <span>创建日期：{item.createdAt}</span>
                        <span>更新日期：{item.updatedAt}</span>
                        <span>所有者：{item.real_name ? item.real_name : '-'}</span>
                      </div>
                      <span className='content-right' onClick={this.InitiateProcess.bind(this, item)}>发起</span>
                    </li>
                  }) : null
                }
              </ul>
            }
          </PullToRefresh> : null
        }
      </div>
    )
  }
}

export default Order
