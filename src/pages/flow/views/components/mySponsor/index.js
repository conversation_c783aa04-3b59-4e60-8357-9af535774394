import React, { PureComponent } from 'react'
import ReactDOM from 'react-dom'
import { SwipeAction, Modal, PullToRefresh, Result, ListView, Toast } from 'antd-mobile'
import Confirm from 'components/confirm'
// import NavTopBar from 'components/NavTopBar'
// import Search from '../components/search'
import API from '../apis'
import './index.less'
let searchValue
class MySponsor extends PureComponent {
  constructor(props) {
    super(props);
    const dataSource = new ListView.DataSource({
      rowHasChanged: (row1, row2) => row1 !== row2,
    });
    this.state = {
      searchParams: {
        page: 1,
        page_size: 50
      },
      NowPage: 1,
      total: 0,
      IsGoReq: 0,
      damping: 60,
      dataSource,
      statusObj: {
        0: '待审批',
        1: '审批中',
        2: '已审批',
        3: '驳回',
        4: '撤回',
        5: '草稿'
      },
      FormIdValue: undefined,
      num: undefined,
      flag: false,
      refreshing: true,
      isLoading: true,
      height: document.documentElement.clientHeight,
      hasMore: true,
      ifEmpty: false
    }
    this.pageIndex = 1
    this.props.onRef(this)
  }

  componentDidUpdate () {
    document.body.style.overflow = 'hidden'
  }

  componentDidMount() {
    const hei = this.state.height - ReactDOM.findDOMNode(this.lv).offsetTop - document.getElementsByClassName('pending-search-wrapper')[0].offsetHeight
    setTimeout(async () => {
      const data = await this.getMyFlow()
      this.rData = data?.data?.task || []
      data && data.data.task.forEach(item => {
        item.status = item.process.status
        if (item.status === 0 || item.status === 1) {
          item.colorValue = '#F19725'
        } else if (item.status === 2) {
          item.colorValue = '#40B335'
        } else if (item.status === 3) {
          item.colorValue = '#EF3D3D'
        } else {
          item.colorValue = '#B7B7B7'
        }
      })
      this.setState({
        dataSource: this.state.dataSource.cloneWithRows(this.rData),
        height: hei,
        refreshing: false,
        isLoading: false,
        ...(this.rData.length ? {} : { ifEmpty: true })
      });
    }, 1000)
  }

  onRefresh = () => {
    this.setState({ refreshing: true, isLoading: true });
    setTimeout(async () => {
      this.pageIndex = 1
      const data = await this.getMyFlow()
      this.rData = data.data.task
      data.data.task.forEach(item => {
        item.status = item.process.status
        if (item.status === 0 || item.status === 1) {
          item.colorValue = '#F19725'
        } else if (item.status === 2) {
          item.colorValue = '#40B335'
        } else if (item.status === 3) {
          item.colorValue = '#EF3D3D'
        } else {
          item.colorValue = '#B7B7B7'
        }
      })
      this.setState({
        dataSource: this.state.dataSource.cloneWithRows(data.data.task),
        refreshing: false,
        isLoading: false,
      });
    }, 600);
  }

  onEndReached = (event) => {
    // load new data
    // hasMore: from backend data, indicates whether it is the last page, here is false
    if (this.state.isLoading && !this.state.hasMore) {
      return;
    }
    this.setState({ isLoading: true });
    setTimeout(async () => {
      ++this.pageIndex
      const data = await this.getMyFlow()
      this.rData = this.rData.concat(data.data.task)
      if (!data.data.task.length) {
        this.setState({
          isLoading: false,
          hasMore: false
        })
        return
      }
      data.data.task && data.data.task.forEach(item => {
        item.status = item.process.status
        if (item.status === 0 || item.status === 1) {
          item.colorValue = '#F19725'
        } else if (item.status === 2) {
          item.colorValue = '#40B335'
        } else if (item.status === 3) {
          item.colorValue = '#EF3D3D'
        } else {
          item.colorValue = '#B7B7B7'
        }
      })
      this.setState({
        dataSource: this.state.dataSource.cloneWithRows(this.rData),
        isLoading: false,
      });
    }, 1000);
  }

  /**
   * 获取我的流程列表
   */
  getMyFlow = (flag) => {
    const { searchParams, myFlowData, NowPage, num } = this.state
    const search = searchParams
    if (num) {
      search.page = num
    }
    if (searchValue && searchValue.indexOf('@inke.cn') !== -1) {
      search.user = searchValue
    } else if (searchValue && searchValue.indexOf('@inke.cn') === -1) {
      search.name = searchValue
    } else {
      delete search.name
      delete search.user
    }
    return API.getMyFlow({
      ...search,
      page: this.pageIndex
    }).catch(err => {
      Toast.fail(err.msg, 1);
    })
  }

  /**
   * 流程审批--撤回
   */
  flowApprove = (item) => {
    API.flowApprove({
      id: item.form.id,
      type: 'recall',
      msg: ''
    }).then(res => {
      // Toast.success('撤回成功')
      Confirm.ToastMessage('撤回成功')
      this.setState({
        FormIdValue: item.form.id,
        num: item.page
      }, () => {
        this.getMyFlow(2)
      })
    }).catch(err => {
      // Toast.fail(err.response.data.msg)
      Confirm.ToastMessage(err.response.data.msg)
    })
  }

  /**
   * 点击展示深审批详情
   * @param { 详情要展示数据 } data
   */
  handleDetailFun = (data) => {
    this.props.history.push({
      pathname: `/contract/approval/list`,
      search: `?id=${data.form.id}`
    })
  }

  renderDom = (status, nextApprover) => {
    if (status === 2) {
      return <span style={{ color: '#40B335' }}>已结束</span>
    } else if (status === 3) {
      return <span style={{ color: '#EF3D3D' }}>驳回</span>
    } else if (nextApprover && nextApprover.length) {
      return <span style={{ color: '#000' }}>
        <span>待办：</span>
        <span style={{ color: '#F19725' }}>{nextApprover[0].real_name}</span>
      </span>
    }
  }

  enterChange = (value) => {
    const dataSource = new ListView.DataSource({
      rowHasChanged: (row1, row2) => row1 !== row2,
    })
    Toast.loading('Loading...', 0)
    searchValue = value
    // this.getMyFlow()
    this.setState({
      dataSource: dataSource.cloneWithRows([]),
      searchParams: {
        page: 1,
        page_size: 50,
      }
    }, async function() {
      this.pageIndex = 1
      const data = await this.getMyFlow()
      this.rData = data.data.task
      data.data.task.forEach(item => {
        item.status = item.process?.status
        if (item.status === 0 || item.status === 1) {
          item.colorValue = '#F19725'
        } else if (item.status === 2) {
          item.colorValue = '#40B335'
        } else if (item.status === 3) {
          item.colorValue = '#EF3D3D'
        } else {
          item.colorValue = '#B7B7B7'
        }
      })
      // const dataSource = new ListView.DataSource({
      //   rowHasChanged: (row1, row2) => row1 !== row2,
      // });
      this.setState({
        dataSource: dataSource.cloneWithRows(data.data.task),
      }, () => {
        Toast.hide()
      });
    })
  }

  render () {
    const { dataSource, ifEmpty } = this.state
    const row = (rowData, sectionID, rowID) => {
      const obj = rowData
      return (
        <div key={rowID} className='mySponsor-li' onClick={this.handleDetailFun.bind(this, obj)}>
          <span className='content-top'>
            <span className='content-arrow-text'>{obj.form.name}</span>
            <span className='content-arrow-icon'></span>
          </span>
          <span className='content-cent'>
            <span>发起时间：{obj.form.createdAt}</span>
            <span>更新时间：{obj.form.updatedAt}</span>
          </span>
          <span className='content-bottom'>
            {
              this.renderDom(obj.status, obj.process.nextApprover)
            }
          </span>
        </div>
      )
    }
    return (
      <>
        {/* <Search enterChange={this.enterChange}></Search> */}
        {
          ifEmpty ? <div className='empty-result'><Result
            img={<img src='https://img.ikstatic.cn/MTYxNDgyNzkzMzQ5NyMxODUjcG5n.png' alt='空' />}
            message="暂无数据"
          /></div> : <ListView
            className='mySponsor-list-view-wrapper'
            key={'1'}
            ref={(ref) => {
              if (ref) {
                this.lv = ref
              }
            }}
            dataSource={dataSource}
            renderFooter={() => (<div className='loading-bottom'>
              {this.state.isLoading ? 'Loading...' : '没有更多'}
            </div>)}
            renderRow={row}
            useBodyScroll={false}
            style={{
              height: this.state.height,
              border: '1px solid #ddd',
              margin: '5px 0',
            }}
            pullToRefresh={<PullToRefresh
              refreshing={this.state.refreshing}
              onRefresh={this.onRefresh}
            />}
            onEndReached={this.onEndReached}
            pageSize={5}
          />
        }
      </>
    )
  }
}

export default MySponsor
