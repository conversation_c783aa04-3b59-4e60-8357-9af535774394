
import React, { useState, useEffect } from 'react'
import NavTopBar from 'components/NavTopBar'
import { Tabs } from 'antd-mobile'
import $utils from 'utils'
import _ from 'lodash'
import './index.less'

const DetailList = (props) => {
  const [list, useList] = useState([])
  const [refund, useRefund] = useState([])
  const [title, useTitle] = useState('')
  const [visibleFields, setVisibleFields] = useState([])

  useEffect(() => {
    GetData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const GetData = () => {
    const d = JSON.parse(sessionStorage.getItem('detailList'))
    const k = JSON.parse(sessionStorage.getItem('refund'))
    const visibleFields = JSON.parse(sessionStorage.getItem('visibleFields'))
    const list = props.location.state?.detailList || d
    const refund = props.location.state?.refund || k
    const visibleKeys = props.location.state?.visibleFields || visibleFields || []
    const title = $utils.getUrlQuery('title')
    useList(list)
    useRefund(refund)
    setVisibleFields(visibleKeys)
    useTitle(title)
  }

  // 渲染tabs子元素
  const tabsContent = (ele) => {
    return <>
      {
        ele.keys.map((element, index) => {
          return (
            <div className='list-infos' key={index}>
              {
                element?.map((item, i) => {
                  return item.value && visibleFields.includes(item.key)
                    ? <div className='list-item' key={i}>
                      <span className='list-text'>{item.field_name}:</span>
                      <span className='list-amount'>{item.value}</span>
                    </div>
                    : ''
                })
              }
            </div>
          )
        })
      }
    </>
  }

  const renderList = (list) => {
    if (_.isArray(list[0])) {
      return list.map((element, index) => {
        return (
          <div className='list-infos' key={index}>
            {
              element?.map((item, i) => {
                return item.value
                  ? <div className='list-item' key={i}>
                    <span className='list-text'>{item.field_name}:</span>
                    <span className='list-amount'>{item.value}</span>
                  </div>
                  : ''
              })
            }
          </div>
        )
      })
    } else {
      const tabs = []
      list.forEach((e, i) => {
        const val = e.keys[0] ? e.keys[0].filter(ele => visibleFields.includes(ele.key)) : []
        if (val.length) {
          tabs.push({
            title: e.title,
            key: i,
            keys: e.keys
          })
        }
      })
      return <Tabs
        tabBarInactiveTextColor="#000"
        tabs={tabs}
        useOnPan
        renderTabBar={props => <Tabs.DefaultTabBar {...props} page={3} />}
      >
        {tabsContent}
      </Tabs>
    }
  }

  return <div className='detail-wrapper'>
    {/* <NavTopBar title={title} backRouter={true} {...props} /> */}
    {
      renderList(list)
    }
    {/* 冲销 */}
    {
      refund && refund.length
        ? refund.map((element, index) => {
          return (
            <div className='list-infos' key={index}>
              {
                element?.map((item, i) => {
                  return item.value
                    ? <div className='list-item' key={i}>
                      <span className='list-text'>{item.field_name}:</span>
                      <span className='list-amount'>{item.value}</span>
                    </div>
                    : ''
                })
              }
            </div>
          )
        })
        : ''
    }
  </div>
}
export default DetailList
