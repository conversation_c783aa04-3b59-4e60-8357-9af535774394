/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-24 14:27:28
 * @LastEditTime: 2021-07-05 12:03:48
 * @LastEditors: Please set LastEditors
 * @Description: 订餐
 */
import React, { PureComponent } from 'react'
import { Modal, But<PERSON> } from 'antd-mobile'
import Confirm from 'components/confirm'
import NavTopBar from 'components/NavTopBar'
import API from '../../../apis'
import './index.less'
import NoApproval from 'assets/flow/contract/approval-dai.png'
import Approvaling from 'assets/flow/contract/approvaling.png'
import ApprovalDone from 'assets/flow/contract/approval-pass.png'
import ApprovalNo from 'assets/flow/contract/approval-no.png'
import ApprovalChe from 'assets/flow/contract/approval-che.png'
import ApprovalCaogao from 'assets/flow/contract/approval-caogao.png'
import { formKeys, fieldsIds, newExpenseFormKeys } from './constant'
import $utils from 'utils'
// import BackApproval from './backCompoent'
import ProcessFlow from './processFlow'
import setTitle from 'components/setBarTitle'
import { AppendixView } from 'utils/appendix'

class ApprovalDetail extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      urlName: window.location.origin + window.location.pathname + '#/contract/?type=mySponsor',
      approvalDetailDatas: undefined,
      iconShowObj: {
        0: NoApproval,
        1: Approvaling,
        2: ApprovalDone,
        3: ApprovalNo,
        4: ApprovalChe,
        5: ApprovalCaogao
      },
      statusObj: {
        start: '发起',
        0: '待审批',
        1: '审批中',
        2: '同意',
        3: '打回',
        4: '撤回',
        5: '草稿',
        6: '回退', // 回退到非发起人
        7: '取回',
        8: '加签',
        9: '回退' // 回退至发起人
      },
      typeList: [
        {
          name: '所在部门：',
          value: 'dept_name'
        },
        {
          name: '合同编号：',
          value: 'contractNumber'
        },
        {
          name: '申请日期：',
          value: 'createdAt'
        },
        {
          name: '描述：',
          value: 'remark'
        }
      ],
      flowChartDatas: [],
      headerList: [],
      tableList: [],
      advanceList: [],
      totalList: [],
      refundList: [],
      pending: false,
      loginUserInfo: {},
      attachmentProcess: [], // 发起人附件
      visibleFields: [], // 可见字段
    }
    this.approved = []
    this.sendPeople = ''
    this.formId = ''
  }

  componentDidMount() {
    setTitle('审批详情')
    this.getTaskDetail()
    // this.setState({
    //   pending: $utils.getUrlQuery('pending')
    // })
    this.getUserInfo()
  }

  async getUserInfo() {
    try {
      let res = await API.getLoginUser()
      const d = res.data || {}
      this.setState({
        loginUserInfo: d,
      })
    } catch (err) {
      Confirm.ToastMessage(err.msg)
    }
  }

  DataFormats = (dataInfo, array, nextflag, statusFlag) => {
    const data = dataInfo
    const values = data.childNode
    const arr = array
    let num = nextflag
    let flag = data.childNode
    if (num && data.type === 1 && data.status === 0) {
      arr.push({
        lastStatus: statusFlag,
        flag: num,
        type: data.type,
        status: data.status,
        nodeContent: data.nodeContent,
        nodeUserList: data.nodeUserList || [],
        nodeName: data.nodeName
      })
      num = false
    } else {
      arr.push({
        lastStatus: statusFlag,
        flag: false,
        type: data.type,
        status: data.status,
        nodeContent: data.nodeContent,
        nodeUserList: data.nodeUserList || [],
        nodeName: data.nodeName
      })
    }
    if (flag) {
      data.status === 3 ? this.DataFormats(values, arr, num, 3) : this.DataFormats(values, arr, num, 0)
    } else {
      arr[arr.length - 1].lastNode = true
    }
    return arr
  }

  /**
   * 根据任务Id获取任务详情
   */
  getTaskDetail = () => {
    API.getTaskDetail({
      id: $utils.getUrlQuery('id')
    }).then(res => {
      window.sessionStorage.setItem('approvalInfo', JSON.stringify(res.data))
      const dataInfo = res.data.process
      let nextFlag = true
      let arr1 = []
      arr1 = this.DataFormats(dataInfo.nodeConfig, arr1 = [], nextFlag, 0)
      const form = res.data.form || {}
      const fieldsList = form.fields || []
      const templateId = form.template_id
      this.approved = form.approved
      this.sendPeople = dataInfo.real_name
      this.formId = form.id
      let keys
      // 个人报销需单独处理
      const isExpense = templateId === fieldsIds.expense
      const isNewExpense = templateId === fieldsIds.newExpense
      if (isExpense) { // 个人报销(旧)
        const type = fieldsList.filter(item => item.key === 'type')[0]?.value
        keys = formKeys[templateId]?.[type]
      } else if (isNewExpense) { // 个人报销新版
        // keys = formKeys[templateId]
        keys = newExpenseFormKeys(fieldsList)
      } else {
        keys = formKeys[templateId]
      }
      this.classifyList(fieldsList, keys, isExpense || isNewExpense)
      window.attachment = dataInfo.attachment || []
      this.setState({
        flowChartDatas: arr1,
        approvalDetailDatas: res.data,
        pending: $utils.getUrlQuery('pending'),
        attachmentProcess: dataInfo.attachment || [],
        visibleFields: dataInfo?.approval_fields?.visible || []
      })
    }).catch(err => {
      Confirm.ToastMessage(err.msg)
    })
  }

  formatAmount(amount) {
    // 金额进行千分符转换
    // 此处无字段进行金额字段判断，应要求进行'00.00'格式判断，可能会有误判
    if (!isNaN(Number(amount)) && `${amount}`.split('.')[1] && `${amount}`.split('.')[1].length === 2) {
      return Number(amount) >= 1000 ? String(amount).replace(/\B(?=(\d{3})+(?!\d))/g, ',') : amount
    }
    return amount
  }

  classifyList(fieldsList, keys, isExpense) {
    // 根据表格对字段进行区分：明细 => pc表格， 详情 => pc表头+高级筛选
    let headerList = []
    let totalList = []
    let tableList = []
    let advanceList = []
    let refundList = []
    // 个人报销单单独处理，选择冲销时显示冲销数据
    let isRefund
    if (isExpense) {
      const transType = ['whether_write_offs', 'type']
      fieldsList = fieldsList.map(item => {
        if (item.key === 'whether_write_offs') isRefund = item.name === '是'
        if (transType.includes(item.key)) item.value = item.name
        return item
      })
    }
    // 未进行配置的表单全部显示在headerList
    if (keys) {
      fieldsList.forEach(item => {
        // if (keys.header?.includes(item.key)) headerList.push(item)
        if (keys.total?.includes(item.key)) {
          item.value = this.formatAmount(item.value)
          totalList.push(item)
        }
        if (keys.advance?.includes(item.key)) advanceList.push(item)
        if (isRefund && keys.refund?.includes(item.key)) refundList.push(item)
      })
      // 费用类型，事由
      if (keys.header && keys.header.length) {
        headerList = keys.header.map(item => fieldsList.find(e => e.key === item) || {})
      }

      // 表格明细
      const tables = keys.table || []
      if (tables.length) {
        tableList = tables.map(item => {
          if (typeof item === 'string') {
            item = fieldsList.find(e => e.key === item) || {}
          } else {
            if (item.keys && item.keys.length) {
              item.keys = item.keys.map(t => {
                t = fieldsList.find(e => e.key === t) || {}
                return t
              })
            }
          }
          return item
        })
      }
    } else {
      headerList = fieldsList
    }
    this.setState({
      headerList,
      tableList,
      advanceList,
      totalList,
      refundList
    })
  }

  /**
   * 驳回
   * @param { 当前要撤回的数据 } item
   */
  handleRecall = () => {
    // this.child.show(this.approved ? this.approved : [], this.sendPeople, this.formId)
    this.props.history.push({
      pathname: '/backDetail',
      search: `?approved=${JSON.stringify(this.approved)}&&sendPeople=${this.sendPeople}&&formId=${this.formId}`,
    })
  }

  /**
   * 通过
   */
  handlePass = () => {
    const { approvalDetailDatas } = this.state
    Modal.prompt('同意操作', '审批意见(选填)', [
      { text: '取消' },
      { text: '提交',
        onPress: (value) => this.flowApprove(approvalDetailDatas, value, 'approve') },
    ], 'default', '', ['请输入审批意见', ''])
  }

  /**
   * 加签
   */
  countersign = () => {
    console.log('加签')
    sessionStorage.setItem('procAttachment', JSON.stringify(this.state.attachmentProcess))
    this.props.history.push({
      pathname: '/contract/counterSign/content',
      search: `?id=${this.formId}`,
      state: {
        procAttachment: this.state.attachmentProcess
      }
    })
  }

  /**
   * 流程审批--通过 || 驳回
   * @param { 数据信息 } item
   * @param { 审批意见描述 } value
   * @param { 审批类型，repulse：驳回, approve：通过 } flag
   */
  flowApprove = (item, value, flag) => {
    API.flowApprove({
      id: item.form.id,
      type: flag,
      msg: value,
      form_attachment: item.form?.attachment || [],
      proc_attachment: item.process?.attachment || []
    }).then(res => {
      if (flag === 'repulse') {
        // Toast.success('审批驳回', 1)
        Confirm.ToastMessage('审批驳回')
      } else if (flag === 'approve') {
        // Toast.success('审批通过', 1)
        Confirm.ToastMessage('审批通过')
      }
      this.props.history.goBack();
    }).catch(err => {
      // Toast.fail(err.response.data.msg)
      Confirm.ToastMessage(err.response.data.msg)
    })
  }

  /**
   * 点击展示详情列表
   * @param { 类型 } type
   */
  handleDetailList = (type) => {
    const title = type === 'table' ? '明细' : '更多'
    const { tableList, advanceList, refundList } = this.state
    let list = []
    let refund = []

    const dealData = (dealList) => {
      let list = []
      const d = dealList?.forEach((item, i) => {
        if (item.hasOwnProperty('keys') && item.keys.length) {
          if (!list[i]) {
            list[i] = {
              title: item.title || ''
            }
          }
          let tempArr = []
          item.keys.forEach((ele, idx) => {
            ele.fields && ele.fields.forEach((element, index) => {
              if (!tempArr[index]) tempArr[index] = []
              element.value = this.formatAmount(element.value)
              tempArr[index].push(element)
            })
            // list[i].keys = tempArr
          })
          list[i].keys = tempArr
        } else {
          item.fields && item.fields.forEach((element, index) => {
            if (!list[index]) list[index] = []
            element.value = this.formatAmount(element.value)
            list[index].push(element)
          })
        }
        return item
      })
      return list
    }
    if (type === 'table') {
      list = dealData(tableList)
    } else {
      list = [advanceList]
      // 报销单有个冲销数据需单独显示
      if (refundList.length) refund = dealData(refundList)
    }
    sessionStorage.setItem('detailList', JSON.stringify(list))
    sessionStorage.setItem('refund', JSON.stringify(refund))
    sessionStorage.setItem('visibleFields', JSON.stringify(this.state.visibleFields))
    this.props.history.push({
      pathname: '/contract/approvalList/detail',
      search: `?title=${title}`,
      state: {
        detailList: list,
        visibleFields: this.state.visibleFields,
        refund: refund
      }
    })
  }

  onRef = (ref) => {
    this.child = ref;
  }

  isApprove() {
    const { approvalDetailDatas, loginUserInfo } = this.state
    const nextApprover = approvalDetailDatas?.process?.nextApprover
    const index = nextApprover ? nextApprover.findIndex(item => `${item?.user}@inke.cn` === loginUserInfo?.email) : -1
    let flag
    if (index !== -1) {
      flag = true
    } else {
      flag = false
    }
    return flag
  }

  /**
   * 单据详情附件
   * @param {单据详情附件数据} item
   */
  processDetailValue = (item) => {
    const domDiv = <div className='process-detail-annex'>
      {
        item.fields && item.fields.map((ele, i) => {
          return <span key={i} className='process-detail-annex-item' onClick={this.handleAttachmentUrl.bind(this, ele.name, ele.value)}>{ele.value}</span>
        })
      }
    </div>
    if (item.fields && item.fields.length) {
      let isAttachment = false
      try {
        const isUrl = new URL(item.fields[0].name)
        isAttachment = true
      } catch (error) {
        isAttachment = false
      }
      if (item.parts_type === 'attachment' || isAttachment) {
        return domDiv
      }
      return <span>{item.value}</span>
    } else {
      return <span>{item.value}</span>
    }
  }
  // 附件预览
  handleAttachmentUrl = (url, name) => {
    // window.open(url)
    AppendixView(url, name)
  }
  /**
   * 发起人附件
   * @param {附件详情数据} attachment
   */
  sponsorAttachment = (attachment) => {
    return <div className='process-detail-annex'>
      {
        attachment.map((ele, i) => {
          return <span key={i} className='process-detail-annex-item' onClick={this.handleAttachmentUrl.bind(this, ele.fileUrl, ele.fileName)}>{ele.fileName}</span>
        })
      }
    </div>
  }

  render () {
    const { approvalDetailDatas, iconShowObj, headerList, totalList, advanceList, attachmentProcess } = this.state
    return (
      <div className='approval'>
        {/* <NavTopBar title='审批详情' backRouter={true} {...this.props} /> */}
        {/* <BackApproval ref={this.onRef}></BackApproval> */}
        {
          approvalDetailDatas ? <>
            <div className='approval-top'>
              <div className='approval-top-left'>
                <span className='approval-top-name'>{approvalDetailDatas.process.proc_name}</span>
                <span>{approvalDetailDatas.form.real_name} {approvalDetailDatas.form.createdAt}</span>
                {/* <span>{iconShowObj[approvalDetailDatas.process.status]}</span> */}
                <img className='icon-approval' src={iconShowObj[approvalDetailDatas.process.status]} alt='' />
              </div>
              <div className={`approval-top-right approval-top-right${approvalDetailDatas.status}`}></div>
            </div>

            {
              <div className='approval-infos'>
                <div className='infos-details'>
                  <span>单据编号: </span><span>{approvalDetailDatas.process.contractNumber || approvalDetailDatas.process.code}</span>
                </div>
                <div className='infos-details'>
                  <span>申请时间: </span><span>{approvalDetailDatas.form.createdAt}</span>
                </div>
                <div className='infos-details'>
                  <span>申请部门: </span><span>{approvalDetailDatas?.process?.dept_name}</span>
                </div>
              </div>
            }

            <div className='approval-infos'>
              {
                <>
                  {
                    headerList.map((item, i) => {
                      return item.value
                        ? <div className='infos-details' key={i}>
                          <span>{item.field_name}: </span>
                          {
                            this.processDetailValue(item)
                          }
                        </div>
                        : ''
                    })
                  }
                  {
                    totalList.length
                      ? <div className='total-details'>
                        {
                          totalList.map((item, i) => {
                            return <div className='total-list' key={i}>
                              <span className='total-text'>{item.field_name}:</span>
                              <span className='total-amount'>{item.value}</span>
                            </div>
                          })
                        }
                        <p className='go-detail' onClick={() => this.handleDetailList('table')}>明细<span className='go-detail-icon'>{'>'}</span></p>
                      </div>
                      : ''
                  }
                </>
              }
            </div>

            {advanceList.length
              ? <div className='approval-more' onClick={() => this.handleDetailList('advance')}>
                <span>更多信息</span>
                <span style={{ fontSize: '18px' }}>{'>'}</span>
              </div>
              : ''
            }
            {/* 发起人附件 */}
            {
              attachmentProcess.length ? <div className='approval-sponsor-attachment'>
                <span>附件: </span>
                { this.sponsorAttachment(attachmentProcess) }
              </div> : null
            }

            <div className='approval-flow'>
              <p className='approval-flow-title'>流程</p>
              <ProcessFlow dataSource={approvalDetailDatas}></ProcessFlow>
            </div>
          </> : null
        }
        {
          this.state.pending && this.isApprove() ? <div className='form-btn'>
            <Button
              size="small"
              inline
              onClick={this.countersign}
              className='btn-countersign operation-btn'
            >
              加签
            </Button>
            <Button
              size="small"
              inline
              onClick={this.handleRecall}
              className='btn-left operation-btn'
            >
              回退
            </Button>
            <Button
              size="small"
              inline
              onClick={this.handlePass}
              className='btn-right operation-btn'
            >
              同意
            </Button>
          </div> : null
        }
      </div>
    )
  }
}

export default ApprovalDetail
