import React, { useCallback, useEffect, useState, useMemo } from 'react'
import { List, Button, Radio, Space, TextArea, Toast } from 'antd-mobile-v5'
import { useHistory } from 'react-router-dom'
import { connect } from 'react-redux'
import { getAllPeople } from '../../../../../../store/actions/index'
import PopupPeople from 'components/PopupPeople'
import util from 'utils'
import { isEmpty } from 'lodash'
import { countersignTypeList } from './constants'
import API from '../../../../apis'
import './index.less'

const CounterSign = (props) => {
  const {
    allPeople = [],
    dispatch
  } = props
  const stateProcAttachment = props.location.state?.procAttachment
  const sessionProcAttachment = JSON.parse(sessionStorage.getItem('procAttachment') || '[]')
  const procAttachment = !isEmpty(stateProcAttachment) ? stateProcAttachment : sessionProcAttachment

  const history = useHistory()
  const formId = util.getUrlQuery('id')
  const [counterSignVisible, setCounterSignVisible] = useState(false) // 加签审批人弹窗
  const [selectPeople, setSelectPeople] = useState([]) // 加签审批人
  const [counterType, setCounterType] = useState('') // 加签类型
  const [counteReason, setCounteReason] = useState('') // 加签原因

  // 加签/转签成功回调
  const operationApprovalOk = (msg = '操作成功') => {
    Toast.show({
      icon: 'success',
      content: msg,
    })
    history.push('/contract/?type=pending')
  }

  // 加签/转签失败回调
  const operationApprovalFail = (errMsg = '操作失败') => {
    Toast.show({
      icon: 'fail',
      content: errMsg,
    })
  }
  // 加签审批人接口
  const counterSignApproval = (params) => {
    API.counterSignApproval(params).then(res => {
      operationApprovalOk('加签成功！')
    }).catch(err => {
      operationApprovalFail(err.msg)
    })
  }

  // 转签接口
  const transferApproval = (params) => {
    API.transferApproval(params).then(res => {
      operationApprovalOk('转签成功！')
    }).catch(err => {
      operationApprovalFail(err.msg)
    })
  }

  const handleCancal = useCallback(() => {
    setCounterSignVisible(false)
  }, [])

  const handleOk = useCallback((value = []) => {
    handleCancal()
    setSelectPeople(value)
  }, [handleCancal])

  const handleRadioChange = (val) => {
    setCounterType(val)
  }

  const handleCancel = () => {
    history.goBack()
  }

  const handleSubmit = () => {
    if (!selectPeople.length) {
      Toast.show('请选择加签审批人！')
      return
    }
    if (!counterType) {
      Toast.show('请选择加签类型！')
      return
    }
    const counterSignParams = {
      form_id: formId,
      countersign_user: selectPeople.toString(), // 加签人员, 多个逗号隔开
      type: counterType, // 加签类型 pre前置加签/next（后置加签）
      proc_attachment: procAttachment, // 流程附件
      joint_approval: true, // 加签人员是否为并行审批（会签）默认为 false 串行
      msg: !counteReason && counterType === 'next' ? '同意' : counteReason, // 审批信息
    }
    if (counterType === 'transfer') { // 转签
      transferApproval({
        task_id: formId,
        user: selectPeople.toString(),
        msg: counteReason
      })
    } else { // 加签
      counterSignApproval(counterSignParams)
    }
  }

  useEffect(() => {
    if (!allPeople.length) {
      dispatch(getAllPeople())
    }
  }, [allPeople, dispatch])

  return <div className='counter-sign'>
    <List className='counter-sign-approver'>
      <List.Item
        extra={selectPeople.length ? selectPeople.toString() : '请选择'}
        onClick={() => setCounterSignVisible(true)}
      >
        加签审批人
      </List.Item>
    </List>
    {
      useMemo(
        () => (
          <PopupPeople
            visible={counterSignVisible}
            value={selectPeople}
            selectedValueFormat='login_name'
            allPeople={allPeople}
            onCancal={handleCancal}
            onOk={handleOk}
          />
        ),
        [
          counterSignVisible,
          selectPeople,
          allPeople,
          handleCancal,
          handleOk
        ]
      )
    }
    <div className='white-space-sign'></div>
    <List header={<span className='is-required counter-sign-wrapper-title'>加签类型</span>}>
      <List.Item>
        {
          <Radio.Group value={counterType} onChange={handleRadioChange}>
            <Space direction='vertical' block>
              {
                countersignTypeList.map((item, i) => (
                  <Radio key={i} value={item.value}>{item.label}</Radio>
                ))
              }
            </Space>
          </Radio.Group>
        }
      </List.Item>
    </List>
    <div className='white-space-sign'></div>
    <List header={<span className='counter-sign-wrapper-title'>加签理由</span>}>
      <List.Item>
        <TextArea
          placeholder="加签理由(选填)"
          rows={4}
          maxLength={200}
          showCount
          onChange={(val) => {
            setCounteReason(val)
          }}
        />
      </List.Item>
    </List>
    <div className='approval-btn-wrapper'>
      <Button className='approval-btn-cancel' onClick={handleCancel}>取消</Button>
      <Button
        className='approval-btn-sure'
        color='primary'
        onClick={handleSubmit}
      >
        确定
      </Button>
    </div>
  </div>
}
export default connect(store => ({
  allPeople: store.state.allPeople
}))(CounterSign)
