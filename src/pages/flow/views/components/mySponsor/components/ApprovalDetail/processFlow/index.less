.process-node-wrapper {
  .process-node {
    display: flex;
  }
  .process-node-left {
    width: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 160px;
    &::after {
      content: '';
      height: 100%;
      display: inline-block;
      width: 2px;
      background: #c9c9c9;
    }
  }
  .end-node-after {
    &::after {
      content: '';
      height: 0;
    }
  }
  .process-node-right {
    flex: 1;
    position: relative;
    top: -4px;
    font-size: 25px;
    margin-left: 10px;
  }
  .process-node-right-top {
    display: flex;
    justify-content: space-between;
  }
  .process-node-right-bottom {
    background: #f2f2f2;
    padding: 10px;
    border-radius: 5px;
    margin: 15px 0 0px 0;
    color: #A1A1A1;
    font-size: 25px;
  }
  .process-node-right-right {
    display: flex;
    flex-direction: column;
  }
  .process-node-right-left {
    display: flex;
    flex-direction: column;
  }
  .start-node {
    display: inline-block;
    width: 48px;
    height: 48px;
    background: url('~src/assets/flow/contract/start-node.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .node-done {
    display: inline-block;
    width: 52px;
    height: 52px;
    background: url('~src/assets/flow/contract/status-ok.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .node-doing {
    display: inline-block;
    width: 52px;
    height: 52px;
    background: url('~src/assets/flow/contract/status-doing.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .end-node {
    display: inline-block;
    width: 30px;
    height: 30px;
    background: url('~src/assets/flow/contract/end-pass.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .end-node-undone {
    display: inline-block;
    width: 30px;
    height: 30px;
    background: url('~src/assets/flow/contract/end.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .process-driver {
    min-height: 100px;
    display: inline-block;
    width: 2px;
    background: #c9c9c9;
  }
  .title-color {
    color: #40B335;
  }
  .process-node-file {
    margin-top: 5px;
    display: flex;
    padding: 10px 0;
  }
  .process-node-img {
    display: inline-block;
    width: 30px;
    height: 30px;
    background: url('~src/assets/home/<USER>') no-repeat;
    background-position: center;
    background-size: contain;
    margin-top: 3px;
  }
  .porcess-node-file-list {
    margin-left: 10px;
    display: flex;
    flex-direction: column;
    .porcess-node-file-list-item {
      overflow:hidden; //超出的文本隐藏
      text-overflow:ellipsis; //溢出用省略号显示
      white-space:nowrap; // 不换行
      max-width: 550px;
      color: #1890ff;
      margin-bottom: 10px !important;
    }
  }
  .hide {
    display: none;
  }
  .approval-people {
    overflow: hidden; //超出的文本隐藏
    text-overflow: ellipsis; //溢出用省略号显示
    white-space: nowrap; //溢出不换行
    display: inline-block;
    width: 200px;
  }
}
.am-action-sheet {
  max-height: 550px;
  overflow-y: auto;
  z-index: 100;
}