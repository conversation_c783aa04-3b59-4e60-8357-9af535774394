import React, { memo, useState, forwardRef, useImperativeHandle, useEffect } from 'react'
import { List, Radio, Picker, WhiteSpace, TextareaItem, Button, Toast } from 'antd-mobile'
import './index.less'
import API from './apis'
import { useHistory } from 'react-router-dom';
import util from 'utils'
let backMessageValue = ''
let formId = ''

const BackApproval = (props, ref) => {
  const [open, setOpen] = useState(false)
  const [backNodeValue, setBackNodeValue] = useState(1)
  const [approval, setApproval] = useState([])
  const [nodeValue, setNodeValue] = useState('')
  const [backNodeAfterValue, setBackNodeAfterValue] = useState(1)
  const history = useHistory()
  useEffect(() => {
    setOpen(true)
    formId = util.getUrlQuery('formId')
    const tempArr = JSON.parse(util.getUrlQuery('approved'))
    const sendName = util.getUrlQuery('sendPeople')
    const arr = tempArr.map(item => ({
      ...item,
      label: `${item.approver_group_name}(${item.real_name})`,
      value: item.approver_group_id
    }))
    arr.unshift({
      label: `发起人(${sendName})`,
      value: 'start'
    })
    setApproval(arr)
  }, [])
  // useImperativeHandle(ref, () => ({
  //   show: (approved = [], sendName, formIds) => {
  //     setOpen(true)
  //     formId = formIds
  //     const arr = approved.map(item => ({
  //       ...item,
  //       label: `${item.approver_group_name}(${item.real_name})`,
  //       value: item.approver_group_id
  //     }))
  //     arr.unshift({
  //       label: `发起人(${sendName})`,
  //       value: 'start'
  //     })
  //     setApproval(arr)
  //   }
  // }))

  const onChange = (value) => {
    setBackNodeValue(value)
    setNodeValue('')
  }

  const changeOther = () => {
    setBackNodeValue(0)
  }

  const changeNodeValue = (val) => {
    setNodeValue(val[0])
  }

  const onChangeBackAfter = (value) => {
    setBackNodeAfterValue(value)
  }

  /**
   * 回退
   * 1 回退到上一步
   * 2 回滚至指定节点
   * 3 回退到发起人
   */
  const sure = () => {
    if (!backNodeValue && !nodeValue) {
      Toast.fail('请选择回退到的节点', '1')
      return
    }
    API.backApprove({
      form_id: formId,
      roll_back_type: nodeValue === 'start' ? 3 : backNodeValue,
      ...((backNodeValue === 2 && nodeValue !== 'start') ? { approver_group_id: nodeValue.toString() } : {}), // 回退到指定节点时，这个字段不能为空
      ...(backMessageValue ? { msg: backMessageValue } : {}), // 回退意见这个字段可以为空
      node_back_after: backNodeAfterValue
    }).then(res => {
      Toast.success('回退成功', 1)
      setTimeout(() => {
        history.push({
          pathname: `/contract`,
          search: `?type=pending`
        })
      }, 1000)
    }).catch(err => {
      Toast.fail(err.msg, '1')
    })
  }

  // 取消
  const cancel = () => {
    setOpen(false)
  }

  const backMessageValueChange = (value) => {
    backMessageValue = value
  }

  const data = [
    { value: 1, label: '上一个节点' },
    { value: 3, label: '发起人' },
  ]
  const backNodeBackData = [
    { value: 1, label: '重走流程' },
    { value: 2, label: '直接提交给我' },
  ]
  return <div className={open ? 'back-approval-wrapper' : 'back-approval-wrapper hide'}>
    <div className='back-approval-content-top'>
      <List renderHeader={() => '请选择回退到的节点'}>
        {data.map(i => (
          <Radio.RadioItem key={i.value} checked={backNodeValue === i.value} onChange={() => onChange(i.value)}>
            {i.label}
          </Radio.RadioItem>
        ))}
      </List>
      <List>
        <Picker data={approval} cols={1} extra="请选择" className="forss" value={[nodeValue]} onChange={changeNodeValue}>
          <List.Item arrow="horizontal" onClick={changeOther}>其他节点</List.Item>
        </Picker>
      </List>
      <WhiteSpace style={{ background: '#eee' }} />
      <List renderHeader={() => '回退节点复审后'}>
        {backNodeBackData.map(i => (
          <Radio.RadioItem key={i.value} checked={backNodeAfterValue === i.value} onChange={() => onChangeBackAfter(i.value)}>
            {i.label}
          </Radio.RadioItem>
        ))}
      </List>
      <WhiteSpace style={{ background: '#eee' }} />
      <List renderHeader={() => '回退理由'}>
        <TextareaItem
          placeholder="请输入回退理由(选填)"
          rows={5}
          count={200}
          onChange={backMessageValueChange}
        />
      </List>
      <div className='approval-btn-wrapper'>
        <Button className='approval-btn-cancel' inline onClick={cancel}>取消</Button>
        <Button className='approval-btn-sure' inline onClick={sure}>确定</Button>
      </div>
    </div>
  </div>
}
export default memo(forwardRef(BackApproval))
