/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState, useCallback, memo } from 'react'
import Confirm from 'components/confirm'
import MultipleApprover from './multipleApprover'
import { AppendixView } from 'utils/appendix'
import API from './apis'
import './index.less'
let flowInitiateName = ''
let flowInitiateTime = ''
let nextApprovalId = ''

const ProcessFlow = (props) => {
  const [data, setData] = useState([])
  const [logData, setLogData] = useState([])
  const [endNodeStatus, setEndNodeStatus] = useState(false)
  const [multipleApproverArr, setMultipleApproverArr] = useState([])
  const [isOpen, setIsOpen] = useState(false)
  const statusObj = {
    start: '发起',
    0: '待审批',
    1: '审批中',
    2: '同意',
    3: '打回',
    4: '撤回',
    5: '草稿',
    6: '回退', // 回退到非发起人
    7: '取回',
    8: '加签-我之后', // 后置加签(我之后)
    9: '回退', // 回退至发起人
    10: '减签',
    11: '转签',
    12: '加签-我之前', // 前置加签(我之前)
  }
  // 递归取出各个节点
  // const dg = useCallback((data, arr = []) => {
  //   if (!data.startNode) {
  //     arr.push({
  //       roleName: data.nodeContent,
  //       attachment: data.attachment,
  //       nodeUserList: data.nodeUserList,
  //       status: data.status
  //     })
  //     if (data.childNode) {
  //       dg(data.childNode, arr)
  //     }
  //   }
  //   return arr
  // }, [])

  useEffect(() => {
    if (Object.keys(props.dataSource).length) {
      const processObj = props.dataSource.process
      flowInitiateName = processObj.real_name
      flowInitiateTime = processObj.createdAt
      nextApprovalId = processObj.nextApprover[0]?.approver_group_id
      const tempArr = digui(processObj.nodeConfig, [])
      setData(tempArr)
      if (processObj.status !== 0) {
        getApprovalLogs(processObj.formId)
      }

      if (processObj.status === 2) {
        setEndNodeStatus(true)
      } else {
        setEndNodeStatus(false)
      }
    }
  }, [props.dataSource])

  const digui = (data, arr = []) => {
    if (!data.startNode) {
      arr.push({
        roleName: data.nodeContent,
        attachment: data.attachment ? data.attachment : [],
        nodeUserList: data.nodeUserList,
        status: data.status,
        type: data.type,
        miss: data.miss
      })
    }
    if (data.childNode) {
      digui(data.childNode, arr)
    }
    return arr
  }

  useEffect(() => {
    if (logData.length) {
      const arr = []
      logData.forEach(item => {
        arr.push({
          roleName: item.nodeContent,
          attachment: item.attachment ? item.attachment : [],
          nodeUserList: item.nodeUserList,
          status: item.status,
          type: item.type,
          miss: item.miss,
          showDoneIcon: true // 这个字段标识该节点属于log中的节点，要展示成完成的状态图标，不依赖status的状态
        })
      })
      // 过滤掉已经审批过的节点既是状态为2的节点
      const filterData = data.filter(item => item.status !== 2)
      const tempArr = arr.concat(filterData)
      setData(tempArr)
    }
  }, [logData])

  // 获取操作日志
  const getApprovalLogs = (id) => {
    API.getApprovalLogs({
      task_id: id
    }).then(res => {
      setLogData(res.data.logs ? res.data.logs : [])
    }).catch(err => {
      Confirm.ToastMessage(err.response.data.msg)
    })
  }

  const renderDom = (item) => {
    if (item && item[0].msg) {
      return <div className='process-node-right-bottom'>{item[0].msg}</div>
    } else {
      return ''
    }
  }

  // 附件
  const renderFileDom = (item) => {
    if (item.attachment.length) {
      return <div className='process-node-file'>
        <span className='process-node-img'></span>
        <div className='porcess-node-file-list'>
          {
            item.attachment.map((list, i) => {
              return <p key={i} className='porcess-node-file-list-item' onClick={handleAttachmentUrl.bind(this, list.fileUrl, list.fileName)}>{list.fileName}</p>
            })
          }
        </div>
      </div>
    }
  }

  // 附件预览
  const handleAttachmentUrl = (url, name) => {
    // window.open(url)
    AppendixView(url, name)
  }

  // 展示状态图标
  const renderStatusIcon = (item) => {
    if (item.showDoneIcon || item.status === 2) {
      return <span className='node-done'></span>
    } else if (item.status !== 2) {
      return <span className='node-doing'></span>
    } else {
      return <span className='start-node'></span>
    }
  }

  // 处理审批状态的展示和审批人的展示
  const renderOption = (item) => {
    if (!item.showDoneIcon && item.status !== 2) {
      return <>
        <span style={{ marginBottom: 5, color: '#F19726' }}>{statusObj[item.status]}</span>
        <span>{item.roleName}</span>
      </>
    } else {
      // type: 2，跳过节点颜色区分
      const colorValue = item.type === 2 ? '#3296fa' : '#40B335'
      return <>
        <span style={{ marginBottom: 5, color: colorValue }}>{statusObj[item.status]}</span>
        <span>{item.roleName}</span>
      </>
    }
  }
  // 点击显示多个审批人
  const handleIsOpenMultipleApprover = (arr) => {
    setIsOpen(true)
    setMultipleApproverArr(arr || [])
  }
  // 点击隐藏多个审批人
  const handleIsDownMultipleApprover = () => {
    setIsOpen(false)
  }

  // 处理审批人，log里的节点和审批通过的节点显示一个审批人，下一个审批人显示多个显示不完出现省略号 其余节点不显示审批人
  const optionApprovalPeople = (item) => {
    // if (item.nodeUserList && item.nodeUserList[0].approver_group_id === nextApprovalId) {
    //   const arr = []
    //   if (item.nodeUserList) {
    //     item.nodeUserList.forEach(list => {
    //       arr.push(list.real_name)
    //     })
    //   }
    //   return <span style={{ textAlign: 'right', marginBottom: 5 }} className='approval-people' onClick={() => arr.length > 1 ? handleIsOpenMultipleApprover(arr) : null}>{arr.toString()}</span>
    // }
    if (item.showDoneIcon || item.status === 2) {
      return <span style={{ textAlign: 'right', marginBottom: 5 }}>{item.nodeUserList && item.nodeUserList[0].real_name}</span>
    } else {
      const arr = []
      if (item.nodeUserList) {
        item.nodeUserList.forEach(list => {
          arr.push(list.real_name)
        })
      }
      return <span style={{ textAlign: 'right', marginBottom: 5 }} className='approval-people' onClick={() => arr.length > 1 ? handleIsOpenMultipleApprover(arr) : null}>{arr.toString()}</span>
    }
  }

  return <div className='process-node-wrapper'>
    {
      isOpen ? <MultipleApprover multipleApproverArr={multipleApproverArr} handleIsDownMultipleApprover={handleIsDownMultipleApprover}></MultipleApprover> : null
    }
    <div className='process-node'>
      <div className='process-node-left'>
        <span className='start-node'></span>
      </div>
      <div className='process-node-right'>
        <div className='process-node-right-top'>
          <div className='process-node-right-right'>
            <span className='title-color' style={{ marginBottom: 3 }}>发起</span>
            <span>发起人</span>
          </div>
          <div className='process-node-right-left'>
            <span style={{ textAlign: 'right', marginBottom: 3 }}>{flowInitiateName}</span>
            <span>{flowInitiateTime}</span>
          </div>
        </div>
        <div style={{ height: 20 }}></div>
      </div>
    </div>
    {
      data.map((item, i) => {
        return <div className={item.miss && item.type !== 2 ? 'process-node hide' : 'process-node'} key={i}>
          <div className='process-node-left'>
            {
              renderStatusIcon(item)
            }
          </div>
          <div className='process-node-right'>
            <div className='process-node-right-top'>
              <div className='process-node-right-right'>
                {
                  renderOption(item)
                }
              </div>
              <div className='process-node-right-left'>
                {
                  optionApprovalPeople(item)
                }
                <span>{item.nodeUserList ? item.nodeUserList[0]?.timestamp : ''}</span>
              </div>
            </div>
            {
              renderDom(item.nodeUserList)
            }
            {
              renderFileDom(item)
            }
            <div style={{ height: 20 }}></div>
          </div>
        </div>
      })
    }
    <div className='process-node'>
      <div className='process-node-left end-node-after'>
        <span className={endNodeStatus ? 'end-node' : 'end-node-undone'}></span>
      </div>
      <div className='process-node-right'>
      </div>
    </div>
  </div>
}
export default memo(ProcessFlow)
