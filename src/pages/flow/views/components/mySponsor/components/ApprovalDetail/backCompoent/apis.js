
import { getRequestsByRoot } from 'axios-service'
import srcConfig from 'src/config/apiRoots'

const { get, post, postXForm, restFulDelete } = getRequestsByRoot({ root: srcConfig.familyWorkFlow })
class Apis {
  /**
    * 流程回退
    * http://yapi.inkept.cn/project/607/interface/api/8808
    */
   backApprove = post('workflow/api/v1/workflow/task/rollback', {}, { autoLoading: false })
}

export default new Apis()
