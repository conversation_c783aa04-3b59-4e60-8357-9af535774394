export const fieldsIds = {
  newExpense: '62314d952215d92971d53f74', // 个人报销单新版
  expense: '606c3f03a616f29c4fe5673b', // 个人报销单
  refund: '607697abd340945d1be660ed', // 还款
  borrow: '6073afd350a9430636e6a1ad', // 借款
  company: '607815341504360aa02258c6', // 对公付款
  confirm: '607fe44ce86a54a4a45a57fa', // 对公费用确认
  contract: '607fe7cde86a54a4a45a57fb', // 合同
  suppleContract: '608132163befa4ebff6aaa9c', // 补充合同
  terminateContract: '6082734456e314fd000f6b59', // 终止合同
  fundsTransfer: '608133153befa4ebff6aaa9d', // 内部流转
  supplier: '60a32631fa3869813648969c', // 供应商增加
  license: '609b7f4276d155db670cce95', // 证照
  sealsUse: '609b7e6776d155db670cce94', // 印章使用
  departmentNew: '60dc1d1ee530ae3be12f9094', // 部门新增
  purchasing: '60bdd7afb26f1822ced178b5', // 采购
}

// 报销类型
export const PARTY_BUILD = 1 // 团建
export const TRAVEL = 2 // 差旅
export const COMPOSITE = 3 // 综合
// 招待费用
export const ENTERTAIN_NO = '无'
export const ENTERTAIN_YES = '有'
// 报销内容
export const EXPENSE_ENTERTAIN = '招待'
export const EXPENSE_OTHER = '其他'
export const EXPENSE_BOTH = '招待+其他'

// 多表格表单
export const newExpenseFormKeys = (allFields = []) => {
  const verdictTableDetailType = () => {
    // 费用类型
    const formType = allFields.find(e => e.key === 'type')?.value || ''
    // 招待费用
    const entertainType = allFields.find(e => e.key === 'have_entertain_fee')?.value || ''
    // 报销内容
    const expenseContentType = allFields.find(e => e.key === 'from_content')?.value || ''
    if (formType === PARTY_BUILD) { // 团建
      return PARTY_BUILD
    } else if (formType === TRAVEL) { // 差旅
      return entertainType
    } else if (formType === COMPOSITE) { // 综合
      return expenseContentType
    }
  }
  const fieldsKeys = {
    [PARTY_BUILD]: {
      title: '团建费用',
      header: ['type', 'reason'],
      total: ['total_amount', 'real_amount'],
      table: [{
        title: '团建费用',
        keys: ['project', 'dept_name', 'amount']
      }],
      advance: ['from_person', 'form_dept', 'currency', 'payment_type', 'org_name', 'org_bank',
        'org_bank_account', 'receipt_person', 'bank_name', 'bank_account', 'borrow_balance_total',
        'whether_write_offs', 'remark'],
      refund: ['borrow_order_no', 'borrow_money', 'write_offs', 'borrow_balance']
    },
    [ENTERTAIN_NO]: {
      header: ['type', 'have_entertain_fee', 'reason'],
      total: ['total_tax_amount', 'toal_no_tax_amount', 'total_amount', 'real_amount'],
      table: [{
        title: '差旅费用',
        keys: ['project', 'dept_name', 'request_form', 'traffic_fee', 'hotel_fee', 'city_traffic_fee', 'meal_fee', 'other_fee', 'tax_amount', 'no_tax_amount']
      }],
      advance: ['from_person', 'form_dept', 'currency', 'payment_type', 'org_name', 'org_bank',
        'org_bank_account', 'receipt_person', 'bank_name', 'bank_account', 'borrow_balance_total',
        'whether_write_offs', 'remark'],
      refund: ['borrow_order_no', 'borrow_money', 'write_offs', 'borrow_balance']
    },
    [ENTERTAIN_YES]: {
      header: ['type', 'have_entertain_fee', 'reason'],
      total: ['total_tax_all', 'total_tax_exclusive_all', 'total_tax_amount_all', 'total_actual_pay_all'],
      table: [{
        title: '差旅费用',
        keys: ['project', 'dept_name', 'request_form', 'traffic_fee', 'hotel_fee', 'city_traffic_fee', 'meal_fee', 'other_fee', 'tax_amount', 'no_tax_amount']
      }, {
        title: '招待费用',
        keys: ['entertainment_Project', 'entertain_dept', 'entertain_amount', 'entertain_date', 'detail_reason', 'entertain_effect']
      }],
      advance: ['from_person', 'form_dept', 'currency', 'payment_type', 'org_name', 'org_bank',
        'org_bank_account', 'receipt_person', 'bank_name', 'bank_account', 'borrow_balance_total',
        'whether_write_offs', 'remark'],
      refund: ['borrow_order_no', 'borrow_money', 'write_offs', 'borrow_balance']
    },
    [EXPENSE_ENTERTAIN]: {
      header: ['type', 'from_content', 'reason'],
      total: ['entertain_total_amount', 'real_amount'],
      table: [{
        title: '招待费用',
        keys: ['entertainment_Project', 'entertain_dept', 'entertain_amount', 'entertain_date', 'detail_reason', 'entertain_effect']
      }],
      advance: ['from_person', 'form_dept', 'currency', 'payment_type', 'org_name', 'org_bank',
        'org_bank_account', 'receipt_person', 'bank_name', 'bank_account', 'borrow_balance_total',
        'whether_write_offs', 'remark'],
      refund: ['borrow_order_no', 'borrow_money', 'write_offs', 'borrow_balance']
    },
    [EXPENSE_OTHER]: {
      header: ['type', 'from_content', 'reason'],
      total: ['total_tax_amount', 'toal_no_tax_amount', 'total_amount', 'real_amount'],
      table: [{
        title: '其他费用',
        keys: ['project', 'dept_name', 'account_project', 'cost_type', 'amount', 'tax_rate', 'no_tax_amount', 'tax_amount', 'tax_diff']
      }],
      advance: ['from_person', 'form_dept', 'currency', 'payment_type', 'org_name', 'org_bank',
        'org_bank_account', 'receipt_person', 'bank_name', 'bank_account', 'borrow_balance_total',
        'whether_write_offs', 'remark'],
      refund: ['borrow_order_no', 'borrow_money', 'write_offs', 'borrow_balance']
    },
    [EXPENSE_BOTH]: {
      header: ['type', 'from_content', 'reason'],
      total: ['total_tax_all', 'total_tax_exclusive_all', 'total_tax_amount_all', 'total_actual_pay_all'],
      table: [{
        title: '其他费用',
        keys: ['project', 'dept_name', 'account_project', 'cost_type', 'amount', 'tax_rate', 'no_tax_amount', 'tax_amount', 'tax_diff']
      }, {
        title: '招待费用',
        keys: ['entertainment_Project', 'entertain_dept', 'entertain_amount', 'entertain_date', 'detail_reason', 'entertain_effect']
      }],
      advance: ['from_person', 'form_dept', 'currency', 'payment_type', 'org_name', 'org_bank',
        'org_bank_account', 'receipt_person', 'bank_name', 'bank_account', 'borrow_balance_total',
        'whether_write_offs', 'remark'],
      refund: ['borrow_order_no', 'borrow_money', 'write_offs', 'borrow_balance']
    }
  }
  const type = verdictTableDetailType()
  return fieldsKeys[type] || {}
}

export const formKeys = {
  [fieldsIds.expense]: {
    1: {
      header: ['type', 'reason'],
      total: ['total_amount', 'toal_no_tax_amount', 'total_tax_amount', 'real_amount'],
      table: ['project', 'account_project', 'cost_type', 'dept_name', 'amount', 'tax_rate', 'no_tax_amount', 'tax_amount', 'tax_diff'],
      advance: ['from_person', 'form_dept', 'currency', 'payment_type', 'org_name', 'org_bank',
        'org_bank_account', 'receipt_person', 'bank_name', 'bank_account', 'borrow_balance_total',
        'whether_write_offs', 'remark'],
      refund: ['borrow_order_no', 'borrow_money', 'write_offs', 'borrow_balance']
    },
    2: {
      header: ['type', 'reason'],
      total: ['total_amount', 'real_amount'],
      table: ['project', 'account_project', 'cost_type', 'dept_name', 'amount'],
      advance: ['from_person', 'form_dept', 'currency', 'payment_type', 'org_name', 'org_bank',
        'org_bank_account', 'receipt_person', 'bank_name', 'bank_account', 'borrow_balance_total',
        'whether_write_offs', 'remark'],
      refund: ['borrow_order_no', 'borrow_money', 'write_offs', 'borrow_balance']
    },
    3: {
      header: ['type', 'reason'],
      total: ['total_amount', 'real_amount'],
      table: ['project', 'account_project', 'cost_type', 'dept_name', 'amount', 'date', 'detail_reason', 'effect'],
      advance: ['from_person', 'form_dept', 'currency', 'payment_type', 'org_name', 'org_bank',
        'org_bank_account', 'receipt_person', 'bank_name', 'bank_account', 'borrow_balance_total',
        'whether_write_offs', 'remark'],
      refund: ['borrow_order_no', 'borrow_money', 'write_offs', 'borrow_balance']
    },
    4: {
      header: ['type', 'reason'],
      total: ['total_amount', 'toal_no_tax_amount', 'total_tax_amount', 'real_amount'],
      table: ['project', 'projectVal', 'account_project', 'cost_type', 'dept_name', 'from_city', 'dest_city', 'vehicle',
        'amount', 'traffic_fee', 'hotel_fee', 'city_traffic_fee', 'meal_fee', 'other_fee', 'tax_amount', 'no_tax_amount'],
      advance: ['from_person', 'form_dept', 'currency', 'payment_type', 'org_name', 'org_bank',
        'org_bank_account', 'receipt_person', 'bank_name', 'bank_account', 'borrow_balance_total',
        'whether_write_offs', 'remark'],
      refund: ['borrow_order_no', 'borrow_money', 'write_offs', 'borrow_balance']
    }
  },
  [fieldsIds.borrow]: {
    header: ['reason'],
    total: ['total_amount', 'total_borrow_balance'],
    table: ['project', 'dept_name', 'amount'],
    advance: ['payment_org', 'org_bank', 'org_bank_account', 'currency', 'payment_type', 'borrow_person', 'loan_dept',
      'bank_name', 'bank_account', 'remark']
  },
  [fieldsIds.refund]: {
    header: ['reason'],
    total: ['total_amount'],
    table: ['loan_no', 'project', 'dept_name', 'borrow_balance', 'current_repayment_amount', 'date'],
    advance: ['person', 'dept', 'currency', 'payment_type', 'collection_org', 'org_bank', 'org_bank_account', 'remark']
  },
  [fieldsIds.fundsTransfer]: {
    header: ['reason', 'org_name', 'org_bank', 'org_bank_account', 'collection_org', 'collection_org_bank', 'collection_org_bank_account'],
    total: ['total_amount'],
    table: ['project', 'account_project', 'dept_name', 'amount_total'],
    advance: ['person', 'dept', 'currency', 'payment_type', 'remark']
  },
  [fieldsIds.company]: {
    header: [
      'expense_type', 'expense_confirmation_form', 'fss_type', 'busi_date', 'type', 'supplier', 'supplier_org_bank',
      'supplier_org_bank_account', 'reason'],
    total: ['total_amount', 'toal_no_tax_amount', 'total_tax_amount'],
    table: ['project', 'account_project', 'cost_type', 'dept_name', 'amount', 'tax_rate', 'no_tax_amount', 'tax_amount',
      'tax_diff', 'receipt', 'contract', 'contract_no'],
    advance: ['person', 'dept', 'currency', 'payment_type', 'org_name', 'org_bank', 'org_bank_account', 'remark']
  },
  [fieldsIds.confirm]: {
    header: ['supplier', 'supplier_org_bank', 'supplier_org_bank_account', 'data_type', 'reason'],
    total: ['total_amount', 'toal_no_tax_amount', 'total_tax_amount'],
    table: ['project', 'account_project', 'cost_type', 'dept_name', 'amount', 'tax_rate', 'no_tax_amount', 'tax_amount',
      'tax_diff', 'receipt', 'contract', 'contract_no'],
    advance: ['person', 'dept', 'currency', 'payment_type', 'org_name', 'org_bank', 'org_bank_account', 'remark']
  },
  [fieldsIds.supplier]: {
    header: ['busi_type', 'main_busi', 'have_pay', 'contract_type', 'contract_content', 'org_name', 'company_name', 'supplier_bank_name',
      'supplier_bank_account', 'busi_lic', 'account_permit', 'honesty_commit', 'contact_person', 'contact_info', 'other_cert'],
    total: [],
    table: [],
    advance: ['person', 'dept', 'supply_type', 'customer_type', 'remark']
  },
  [fieldsIds.contract]: {
    header: ['contract_name', 'contract_type', 'frame', 'contract_commencement_date', 'contract_deadline',
      'contract_period', 'pay_type', 'nature_payment', 'receipt_type', 'org_name', 'supplier1', 'supplier2',
      'supplier3', 'contract_version', 'contract_appendices', 'scanning_copy', 'stamp', 'reason'],
    total: ['total_amount', 'toal_no_tax_amount', 'total_tax_amount'],
    table: ['project', 'payment_type', 'pay_date', 'pay_terms', 'amount', 'tax_rate', 'tax_amount', 'no_tax_amount'],
    advance: ['person', 'dept', 'dept_name', 'standard_currency', 'currency', 'rate', 'remark']
  },
  [fieldsIds.suppleContract]: {
    header: ['contract_master', 'contract_master_appendices', 'contract_name', 'contract_appendices_original',
      'contract_type', 'frame', 'contract_commencement_date', 'contract_deadline', 'contract_period',
      'pay_type', 'nature_payment', 'receipt_type', 'org_name1', 'org_name2', 'org_name3',
      'supplier1', 'supplier2', 'supplier3', 'contract_version', 'contract_appendices', 'scanning_copy', 'stamp', 'reason'],
    total: ['total_amount', 'toal_no_tax_amount', 'total_tax_amount'],
    table: ['project', 'payment_type', 'pay_date', 'pay_terms', 'amount', 'tax_rate', 'tax_amount', 'no_tax_amount'],
    advance: ['person', 'dept', 'dept_name', 'standard_currency', 'currency', 'rate']
  },
  [fieldsIds.terminateContract]: {
    header: ['contract_master', 'contract_master_appendices', 'contract_name', 'contract_type', 'frame', 'contract_commencement_date',
      'pay_type', 'nature_payment', 'receipt_type', 'org_name1', 'org_name2', 'org_name3', 'supplier1',
      'supplier2', 'supplier3', 'contract_version', 'contract_appendices', 'scanning_copy', 'stamp', 'reason'],
    total: ['total_amount', 'toal_no_tax_amount', 'total_tax_amount'],
    table: ['payment_type', 'pay_date', 'pay_terms', 'amount', 'tax_rate', 'tax_amount', 'no_tax_amount'],
    advance: ['person', 'dept', 'dept_name', 'standard_currency', 'currency', 'rate']
  },
  [fieldsIds.license]: {
    header: ['borrow_time', 'return_time', 'lending_day', 'reason', 'actual_borrow_time', 'actual_return_time'],
    total: [],
    table: ['org', 'seal_type', 'name'],
    advance: []
  },
  [fieldsIds.sealsUse]: {
    header: ['file_name', 'Issued_by', 'file_type', 'whether_amount_involved', 'amount_type', 'amount_involved', 'attach', 'reason'],
    total: [],
    table: ['seal_org', 'seal_type'],
    advance: []
  },
  [fieldsIds.purchasing]: {
    header: ['budget_amount', 'actual_amount', 'purchase_category', 'time', 'budget_dept', 'reason', 'purchase_method1',
      'purchase_method', 'quotation', 'price_comparison_table', 'tender_documents'],
    total: [],
    table: ['name', 'model', 'unit', 'estimate_num', 'temporary_unit_price', 'temporary_subtotal', 'actual_num', 'actual_unit_price', 'actual_subtotal'],
    advance: ['person', 'dept', 'unitName']
  },
  [fieldsIds.departmentNew]: {
    header: ['dept_need', 'reason', 'nem_add_dept', 'dept_type', 'dept_fin_org1', 'fin_org_id1', 'superior_dept', 'superior_dept_id', 'dept_fin_org', 'fin_org_id'],
    total: [],
    table: ['modify_dept', 'dept_id', 'modify_dept_type', 'code'],
    advance: ['person', 'dept', 'unit']
  },
}
