.approval{
  height: 100vh;
  overflow: auto;
  .approval-top{
    width: 750px;
    height: 223px;
    background: #FFFFFF;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-size: 26px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 400;
    color: #999999;
    line-height: 36px;
    box-shadow: 0px 20px 0px 0px #F0F5F5;
    .approval-top-left{
      position: relative;
      display: flex;
      flex-direction: column;
      padding-left: 40px;
      span:nth-child(1) {
        color: #000000;
        font-size: 30px;
        line-height: 54px;
        display: inline-block;
        max-width: 400px;
        overflow: hidden;
        // text-overflow: ellipsis;
        // white-space: nowrap;
      }
      .icon-approval {
        width: 120px;
        position: absolute;
        right: -250px;
      }
    }
    .approval-top-right{
      width: 164px;
      height: 164px;
      padding-right: 40px;
    }
    .approval-top-right0{
      background: url('~src/assets/flow/contract/approval-dai.png') no-repeat;
      background-position: center;
      background-size: contain;
    }
    .approval-top-right1{
      background: url('~src/assets/flow/contract/approvaling.png') no-repeat;
      background-position: center;
      background-size: contain;
    }
    .approval-top-right2{
      background: url('~src/assets/flow/contract/approval-pass.png') no-repeat;
      background-position: center;
      background-size: contain;
    }
    .approval-top-right3{
      background: url('~src/assets/flow/contract/approval-no.png') no-repeat;
      background-position: center;
      background-size: contain;
    }
    .approval-top-right4{
      background: url('~src/assets/flow/contract/approval-che.png') no-repeat;
      background-position: center;
      background-size: contain;
    }
    .approval-top-right5{
      background: url('~src/assets/flow/contract/approval-caogao.png') no-repeat;
      background-position: center;
      background-size: contain;
    }
  }
  .approval-infos{
    padding: 20px 40px;
    font-size: 26px;
    // font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 40px;
    box-shadow: 0px 20px 0px 0px #F0F5F5;
    .infos-details{
      margin: 30px 0;
      font-size: 26px;
      // font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #747474;
      p:nth-child(2) {
        font-size: 26px;
        // font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
      }
      span:nth-child(2) {
        // font-size: 14px;
        padding-left: 6px;
        color: #333333;
      }
    }
    .total-details {
      position: relative;
      padding: 8px 16px;
      background: #FAFAFA;
      border-radius: 16px;
      .total-list {
        margin-bottom: 16px;
      }
      .total-list:nth-child(1) {
        margin-top: 16px;
      }
      .total-amount {
        padding-left: 12px;
        color: #FA8C16;
      }
      .go-detail {
        position: absolute;
        right: 10px;
        top: 20px;
        color: #00E0CA;
      }
      .go-detail-icon {
        margin-left: 8px;
      }
    }
  }
  .approval-sponsor-attachment {
    padding: 40px 40px 20px 40px;
    font-size: 26px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 40px;
    box-shadow: 0px 20px 0px 0px #F0F5F5;
  }
  .approval-more {
    display: flex;
    justify-content: space-between;
    padding: 40px 40px 20px 40px;
    font-size: 26px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 40px;
    box-shadow: 0px 20px 0px 0px #F0F5F5;
  }
  .approval-flow{
    padding: 20px 40px;
    font-family: PingFangSC-Medium, PingFang SC;
    .approval-flow-title{
      margin: 30px 0;
      font-size: 34px;
      font-weight: 500;
      color: #000000;
      line-height: 48px;
    }
    .approval-flow-list{
      min-height: 160px;
      // background: #F0F5F5;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      p {
        margin-bottom: 0;
      }
      .list-left{
        width: 100px;
        position: relative;
        .list-icon{
          // width: 100px;
          // height: 100px;
          // background: #EBEBEE;
          // position: relative;
          // border-radius: 50%;
          // z-index: 99;
          img{
            width: 100px;
            height: 100px;
          }
          .icon-status{
            position: absolute;
            bottom: 50%;
            right: 50%;
            transform: translate(50%, 50%);
            width: 32px;
            height: 32px;
          }
          .icon-status-pass{
            background: url('~src/assets/flow/contract/status-ok.png') no-repeat;
            background-position: center;
            background-size: contain;
          }
          .icon-status-nextapprovaling{
            background: url('~src/assets/flow/contract/status-doing.png') no-repeat;
            background-position: center;
            background-size: contain;
          }
          .icon-status-approval-pass{
            background: url('~src/assets/flow/contract/status-ok.png') no-repeat;
            background-position: center;
            background-size: contain;
          }
          .icon-status-approval-no{
            background: url('~src/assets/flow/contract/status-no.png') no-repeat;
            background-position: center;
            background-size: contain;
          }
        }
        .list-line {
          width: 1px;
          min-height: 90px;
          height: 100%;
          border: 1px solid rgba(0,0,0,.25);
          border-left: none;
          margin-left: 11px;
        }
        // .list-line{
        //   width: 2px;
        //   height: 100%;
        //   background: #D8D8D8;
        //   position: absolute;
        //   bottom: 0;
        //   left: 50px;
        //   z-index: 1;
        // }
      }
      .list-right{
        flex: 1;
        min-height: 100px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        padding-left: 30px;
        font-size: 26px;
        font-weight: 400;
        color: #B7B7B7;
        .list-right-top{
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          line-height: 50px;
          span:nth-child(1) {
            font-size: 32px;
            color: #333333;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
          }
          span:nth-child(2) {
            white-space: nowrap;
          }
          .list-right-top-right {
            text-align: right;
            .green {
              color: #00E0CA;
            }
            .org {
              color: #FA8C16;
            }
          }
        }
        .list-right-botm{
          color: #666666;
          // line-height: 42px;
        }
        .approvaling{
          color: #F19725;
        }
        .list-right-botm-approval-no{
          color: #EF3D3D;
        }
      }
    }
  }
  .form-btn{
    // flex: 0 0 auto;
    width: 750px;
    height: 120px;
    background: #FFFFFF;
    box-shadow: 0px -4px 12px 0px rgba(0, 0, 0, 0.06);
    display: flex;
    justify-content: space-around;
    align-items: center;
    position: fixed;
    bottom: 0;
    .operation-btn {
      width: 200px;
      height: 88px;
      border-radius: 10px;
      color: #FFFFFF;
      line-height: 88px;
      font-size: 30px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
    }
    .btn-left{
      background: #EF3D3D;
    }
    .btn-right{
      background: #00C8B7;
    }
    .btn-countersign {
      background: #DB9757;
    }
  }
  .process-detail-annex {
    display: flex;
    flex-direction: column;
    .process-detail-annex-item {
      color: #1891FF !important;
      padding: 0px !important;
      margin: 2px 0;
    }
  }
}