/* eslint-disable react-hooks/exhaustive-deps */
import React from 'react'
import { ActionSheet } from 'antd-mobile';
import './index.less'
const isIPhone = new RegExp('\\biPhone\\b|\\biPod\\b', 'i').test(window.navigator.userAgent);
let wrapProps;
if (isIPhone) {
  wrapProps = {
    onTouchStart: e => e.preventDefault(),
  };
}

const MultipleApprover = (props) => {
  const showActionSheet = () => {
    ActionSheet.showActionSheetWithOptions({
      options: props.multipleApproverArr,
      message: '所有审批人',
      maskClosable: true,
      wrapProps,
    },
    () => {
      props.handleIsDownMultipleApprover()
    });
  }
  return <div>
    {
      showActionSheet()
    }
  </div>
}
export default MultipleApprover
