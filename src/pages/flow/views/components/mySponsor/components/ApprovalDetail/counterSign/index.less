.counter-sign {
  // .adm-list {
  //   --header-font-size: 32px !important;
  // }
  .counter-sign-approver {
    a {
      color: #000000;
    }
  }
  .approval-btn-wrapper {
    position: fixed;
    height: 120px;
    background: #FFFFFF;
    box-shadow: 0px -4px 12px 0px rgba(0, 0, 0, 0.06);
    width: 750px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    bottom: 0;
  }
  .approval-btn-cancel {
    width: 306px;
    height: 88px;
    transform: rotateX(0deg) rotateY(360deg) rotateZ(360deg)
  }
  .approval-btn-sure {
    width: 306px;
    height: 88px;
    background-color: #00C8B7;
    --border-style: #00C8B7;
  }
  .counter-sign-wrapper-title {
    font-size: 32px;
  }
  .is-required {
    &::before {
      content: '*';
      color: #ff4d4f;
    }
  }
  .white-space-sign {
    width: 100vw;
    height: 15px;
    background: #eeeeee;
  }
}
// .people-popup {
//   .people-popup-body {
//     height: 50vh;
//   }
//   .select-people-popup {
//     .operation-btn {
//       display: flex;
//       height: 88px;
//       justify-content: space-between;
//       align-items: center;
//       border-bottom: 1px solid #eee;
//       padding: 4px;
//       .btn-color {
//         color: #25e6d2;
//       }
//     }
//     .people-content {
//       .search {
//         margin: 10px 20px;
//       }
//       .people-list {
//         height: calc(50vh - 100px);
//         overflow-y: scroll;
//         color: #000000;
//         border-top: 1px solid #eee;
//         a {
//           color: #000000;
//         }
//         .adm-check-list-item-active, .adm-check-list-item-extra {
//           color: #25e6d2;
//         }
//       }
//     }
//     .people-popup-title {
//       font-size: 36px;
//     }
//   }
// }
