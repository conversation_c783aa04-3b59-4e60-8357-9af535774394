.mySponsor-list-view-wrapper{
  background: #f2f2f2;
  border: none !important;
  // margin: 100px 0 0 0 !important;
  // top: 100px;
  .list-view-section-body {
    background: #f2f2f2;
    margin-top: 20px;
  }
  .mySponsor-li{
    width: 700px;
    height: 200px;
    box-shadow: 0px 1px 0px 0px #E8E8E8;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 40px;
    font-size: 26px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999999;
    background: #fff;
    margin: 0 auto 20px;
    border-radius: 15px;
    .content-top{
      font-size: 34px;
      font-weight: 500;
      color: #000000;
      line-height: 54px;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .content-arrow-text {
      overflow:hidden;
      text-overflow:ellipsis;
      white-space:nowrap;
    }
    .content-arrow-icon {
      display: inline-block;
      width: 28px;
      height: 28px;
      background: url('~src/assets/flow/contract/arrow.png') no-repeat;
      background-position: center;
      // background-size: contain;
      background-size: cover;
    }
    .content-cent{
      display: flex;
      flex-direction: column;
      span{
        line-height: 40px;
      }
    }
    .content-bottom{
      // display: flex;
      // flex-direction: row;
      // justify-content: space-between;
      // line-height: 42px;
      text-align: right;
      span:nth-child(1) {
        width: 400px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      span:nth-child(2) {
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 500;
        font-size: 26px;
      }
    }
  }
}