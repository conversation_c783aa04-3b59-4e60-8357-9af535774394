.flow{
  position: fixed;
  height: 100%;
  width: 100%;
  top: 0;
  background-color: #ffffff;
  .am-tab-bar-bar{
    position: fixed;
    width: 750px;
    height: 108px;
    background: #FFFFFF;
    box-shadow: 0px -4px 12px 0px rgba(0, 0, 0, 0.06);
  }
  .am-tab-bar-item{
    height: calc(100% - 108px);
    overflow: auto;
  }
  .am-tab-bar-tab-title{
    height: 30px !important;
    font-size: 22px !important;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #000000;
    line-height: 30px !important;
  }
  .flowPage{
    width: 56px;
    height: 56px;
    background: url('~src/assets/visitor/visitorPage.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .flowPage-active{
    width: 56px;
    height: 56px;
    background: url('~src/assets/visitor/visitorPage-active.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .pending{
    width: 56px;
    height: 56px;
    background: url('~src/assets/flow/flow-pending.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .pending-active{
    width: 56px;
    height: 56px;
    background: url('~src/assets/flow/flow-pending-active.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .pended{
    width: 56px;
    height: 56px;
    background: url('~src/assets/flow/flow-pended.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .pended-active{
    width: 56px;
    height: 56px;
    background: url('~src/assets/flow/flow-pended-active.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .mySponsor{
    width: 56px;
    height: 56px;
    background: url('~src/assets/flow/flow-mystart.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .mySponsor-active{
    width: 56px;
    height: 56px;
    background: url('~src/assets/flow/flow-mystart-active.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
}

// .visitor-refresh{
//   height: calc(100vh - 108px - 88px);
//   overflow: auto;
// }