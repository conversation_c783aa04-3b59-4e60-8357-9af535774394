/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-03 14:27:28
 * @LastEditTime: 2020-11-20 20:28:47
 * @LastEditors: Please set LastEditors
 */
import React, { PureComponent, useRef } from 'react'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'
import * as actions from '../../store/actions/index'
import { TabBar } from 'antd-mobile'
import './index.less'
// import FlowPage from './ContractManagement'
import Pending from './pending'
import Pended from './pended'
import MySponsor from './mySponsor'
import setTitle from 'components/setBarTitle'
import Search from '../components/components/search'

const mapStateToProps = state => ({ stores: state.state })
const mapDispatchToProps = dispatch => ({ ...bindActionCreators(actions, dispatch) })
@connect(mapStateToProps, mapDispatchToProps)
class Flow extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      selectedTab: 'pending',
      bottomNavBar: [
        // {
        //   title: '首页',
        //   key: 'flowPage',
        //   url: '#/contract'
        // },
        {
          title: '待处理',
          key: 'pending',
          url: '#/contract/?type=pending'
        },
        {
          title: '已处理',
          key: 'pended',
          url: '#/contract/?type=pended'
        },
        {
          title: '我发起的',
          key: 'mySponsor',
          url: '#/contract/?type=mySponsor'
        }
      ],
      valueId: 0,
      obj: undefined
    };
    setTitle('流程审批')
  }

  componentDidMount () {
    if (!this.props.stores.allPeople.length) {
      this.props.getAllPeople()
    }
    let urlHashValue = window.location.hash
    switch (urlHashValue) {
      case '#/contract/?type=pending':
        this.setState({
          valueId: 0,
          selectedTab: 'pending'
        })
        break;
      case '#/contract/?type=pended':
        this.setState({
          valueId: 1,
          selectedTab: 'pended'
        })
        break;
      case '#/contract/?type=mySponsor':
        this.setState({
          valueId: 2,
          selectedTab: 'mySponsor'
        })
        break;
      default:
        this.setState({
          valueId: 0,
        })
        break;
    }
  }

  /**
   * tabbar点击回调函数
   * @param { 当前tabbar下标 } e
   * @param { 当前tabbar相关参数 } item
   */
  handleNavBarBottomFun = (e, item) => {
    if (e === this.state.valueId) return
    this.search.clearValue()
    switch (e) {
      case 0:
        this.setState({
          valueId: 0,
          selectedTab: 'pending'
        })
        window.location.href = window.location.origin + window.location.pathname + item.url
        setTimeout(() => {
          this.pending.enterChange()
        }, 100)
        break
      case 1:
        this.setState({
          valueId: 1,
          selectedTab: 'pended'
        })
        window.location.href = window.location.origin + window.location.pathname + item.url
        // this.pended.enterChange()
        setTimeout(() => {
          this.pended.enterChange()
        }, 100)
        break
      case 2:
        this.setState({
          valueId: 2,
          selectedTab: 'mySponsor'
        })
        window.location.href = window.location.origin + window.location.pathname + item.url
        // this.mySponsor.enterChange()
        setTimeout(() => {
          this.mySponsor.enterChange()
        }, 100)
        break
      default:
        break
    }
  }

  // 回到首页
  backNowFirstPage = () => {
    this.setState({
      valueId: 0,
      selectedTab: 'flowPage'
    })
    window.location.href = window.location.origin + window.location.pathname + '#/contract'
  }

  /**
   * 点击回车
   */
  enterChange = (value) => {
    if (this.state.valueId === 0) {
      this.pending.enterChange(value)
    } else if (this.state.valueId === 1) {
      this.pended.enterChange(value)
    } else {
      this.mySponsor.enterChange(value)
    }
  }

  render () {
    const { bottomNavBar, selectedTab } = this.state
    return (
      <div className='flow'>
        <Search enterChange={this.enterChange} ref={node => (this.search = node)} keyMark={this.props.keys}></Search>
        <TabBar
          unselectedTintColor="#000000"
          tintColor="#000000"
          barTintColor="#FFFFFF"
          tabBarPosition='bottom'
          className='flow-tabBar'
          prerenderingSiblingsNumber={0}
        >
          {
            bottomNavBar.map((item, index) => {
              return (
                <TabBar.Item
                  title={item.title}
                  key={item.key}
                  icon={<div className={item.key} />}
                  selectedIcon={<div className={`${item.key}-active`} />}
                  selected={selectedTab === item.key}
                  onPress={() => {
                    this.setState({
                      selectedTab: item.key,
                    })
                    this.handleNavBarBottomFun(index, item)
                  }}
                >
                  {
                    this.state.valueId === 0 ? <Pending onRef={node => (this.pending = node)} {...this.props} backNowFirstPage={this.backNowFirstPage} /> : null
                  }
                  {
                    this.state.valueId === 1 ? <Pended onRef={node => (this.pended = node)} {...this.props} backNowFirstPage={this.backNowFirstPage} /> : null
                  }
                  {
                    this.state.valueId === 2 ? <MySponsor onRef={node => (this.mySponsor = node)} {...this.props} backNowFirstPage={this.backNowFirstPage} /> : null
                  }
                </TabBar.Item>
              )
            })
          }
        </TabBar>
      </div>
    )
  }
}

export default Flow
