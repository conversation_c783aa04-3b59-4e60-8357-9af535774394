import React, { PureComponent } from 'react'
import ReactDOM from 'react-dom'
import { SwipeAction, Icon, Modal, PullToRefresh, ListView, Badge, Result, Toast } from 'antd-mobile'
import Confirm from 'components/confirm'
// import NavTopBar from 'components/NavTopBar'
// import Search from '../components/search'
import API from '../apis'
import './index.less'
let searchValue
// const URLName = window.location.origin + window.location.pathname + '#/contract'

class MySponsor extends PureComponent {
  constructor(props) {
    super(props);
    const dataSource = new ListView.DataSource({
      rowHasChanged: (row1, row2) => row1 !== row2,
    });
    this.state = {
      dataSource,
      statusObj: {
        0: '待审批',
        1: '审批中',
        2: '已审批',
        3: '驳回',
        4: '撤回',
        5: '草稿',
        6: '回退',
        7: '取回'
      },
      searchParams: {
        page: 1,
        page_size: 50,
      },
      NowPage: 1,
      total: 0,
      flag: false,
      URLName: window.location.origin + window.location.pathname + '#/contract',
      monthDay: '',
      refreshing: true,
      isLoading: true,
      height: document.documentElement.clientHeight,
      hasMore: true,
      ifEmpty: false
    }
    this.pageIndex = 1
    this.props.onRef(this)
  }

  componentDidMount() {
    const hei = this.state.height - ReactDOM.findDOMNode(this.lv).offsetTop - document.getElementsByClassName('pending-search-wrapper')[0].offsetHeight
    setTimeout(async () => {
      const data = await this.getPendingApproveList()
      this.rData = data?.data?.task || []
      data && data.data.task.forEach(item => {
        item.status = item.process.status
        if (item.status === 0 || item.status === 1) {
          item.colorValue = '#F19725'
        } else if (item.status === 2) {
          item.colorValue = '#40B335'
        } else if (item.status === 3) {
          item.colorValue = '#EF3D3D'
        } else {
          item.colorValue = '#B7B7B7'
        }
      })
      this.setState({
        dataSource: this.state.dataSource.cloneWithRows(this.rData),
        height: hei,
        refreshing: false,
        isLoading: false,
        ...(this.rData.length ? {} : { ifEmpty: true })
      });
    }, 1000)
  }

  componentDidUpdate () {
    document.body.style.overflow = 'hidden'
  }

  onRefresh = () => {
    this.setState({ refreshing: true, isLoading: true });
    setTimeout(async () => {
      this.pageIndex = 1
      const data = await this.getPendingApproveList()
      this.rData = data.data.task
      data.data.task.forEach(item => {
        item.status = item.process.status
        if (item.status === 0 || item.status === 1) {
          item.colorValue = '#F19725'
        } else if (item.status === 2) {
          item.colorValue = '#40B335'
        } else if (item.status === 3) {
          item.colorValue = '#EF3D3D'
        } else {
          item.colorValue = '#B7B7B7'
        }
      })
      this.setState({
        dataSource: this.state.dataSource.cloneWithRows(data.data.task),
        refreshing: false,
        isLoading: false,
      });
    }, 600);
  }

  onEndReached = (event) => {
    // load new data
    // hasMore: from backend data, indicates whether it is the last page, here is false
    if (this.state.isLoading && !this.state.hasMore) {
      return;
    }
    console.log('reach end', event);
    this.setState({ isLoading: true });
    setTimeout(async () => {
      // this.rData = [...this.rData, ...genData(++pageIndex)]
      ++this.pageIndex
      const data = await this.getPendingApproveList()
      this.rData = this.rData.concat(data.data.task)
      if (!data.data.task.length) {
        this.setState({
          isLoading: false,
          hasMore: false
        })
        return
      }
      data.data.task && data.data.task.forEach(item => {
        item.status = item.process.status
        if (item.status === 0 || item.status === 1) {
          item.colorValue = '#F19725'
        } else if (item.status === 2) {
          item.colorValue = '#40B335'
        } else if (item.status === 3) {
          item.colorValue = '#EF3D3D'
        } else {
          item.colorValue = '#B7B7B7'
        }
      })
      this.setState({
        dataSource: this.state.dataSource.cloneWithRows(this.rData),
        isLoading: false,
      });
    }, 1000);
  }

  /**
   * 获取我的流程列表
   */
  getPendingApproveList = () => {
    const { searchParams, dataSource } = this.state
    const tempParams = searchParams
    if (searchValue && searchValue.indexOf('@inke.cn') !== -1) {
      tempParams.user = searchValue
    } else if (searchValue && searchValue.indexOf('@inke.cn') === -1) {
      tempParams.name = searchValue
    } else {
      delete tempParams.name
      delete tempParams.user
    }
    return API.getPendingApproveList({
      ...tempParams,
      page: this.pageIndex
    }).catch(err => {
      Toast.fail(err.msg, 1);
    })
  }

  /**
   * 点击展示深审批详情
   * @param { 详情要展示数据 } data
   */
  handleDetailFun = (data) => {
    this.props.history.push({
      pathname: `/contract/approval/list`,
      search: `?id=${data.form.id}&pending=true`
    })
  }

  enterChange = (value) => {
    const dataSource = new ListView.DataSource({
      rowHasChanged: (row1, row2) => row1 !== row2,
    })
    Toast.loading('Loading...', 0)
    searchValue = value
    this.setState({
      dataSource: dataSource.cloneWithRows([]),
      searchParams: {
        page: 1,
        page_size: 50,
      }
    }, async function() {
      this.pageIndex = 1
      const data = await this.getPendingApproveList()
      this.rData = data.data.task
      data.data.task.forEach(item => {
        item.status = item.process?.status
        if (item.status === 0 || item.status === 1) {
          item.colorValue = '#F19725'
        } else if (item.status === 2) {
          item.colorValue = '#40B335'
        } else if (item.status === 3) {
          item.colorValue = '#EF3D3D'
        } else {
          item.colorValue = '#B7B7B7'
        }
      })
      // const dataSource = new ListView.DataSource({
      //   rowHasChanged: (row1, row2) => row1 !== row2,
      // });
      this.setState({
        dataSource: dataSource.cloneWithRows(data.data.task),
      }, () => {
        Toast.hide()
      });
    })
  }

  optionTime = (time) => {
    if (time) {
      const str = time.split(' ')[0].split('-')
      return `${str[1]}-${str[2]}`
    }
  }

  render () {
    const { dataSource, ifEmpty } = this.state
    const row = (rowData, sectionID, rowID) => {
      const obj = rowData
      return (
        <div className='mySponsor-li' onClick={this.handleDetailFun.bind(this, obj)} key={rowID}>
          <span className='content-top'>
            <span className='content-top-title'>{obj.form.name}</span>
            <Badge dot={obj.process.is_read === 0} style={{ right: -1, top: 1 }}>
              <span className='content-top-date'>{this.optionTime(obj.process.createdAt)}</span>
            </Badge>
          </span>
          <span className='content-cent'>
            <span>申请人：{obj.process.real_name}</span>
            <span>申请时间：{obj.process.createdAt}</span>
          </span>
          <span className='content-bottom'>
            {
              (obj.process.status === 6 || obj.process.status === 7) ? <span className='back-mark'>{this.state.statusObj[obj.status]}</span> : ''
            }
          </span>
        </div>
      )
    }
    return (
      <>
        {/* <Search enterChange={this.enterChange} keyMark={this.props.keys}></Search> */}
        {
          ifEmpty ? <div className='empty-result'><Result
            img={<img src='https://img.ikstatic.cn/MTYxNDgyNzkzMzQ5NyMxODUjcG5n.png' alt='空' />}
            message="暂无数据"
          /></div> : <ListView
            key={'1'}
            className='pending-list-view-wrapper'
            ref={(ref) => {
              if (ref) {
                this.lv = ref
              }
            }}
            dataSource={dataSource}
            renderFooter={() => (<div className='loading-bottom'>
              {this.state.isLoading ? 'Loading...' : '没有更多'}
            </div>)}
            renderRow={row}
            useBodyScroll={false}
            style={{
              height: this.state.height,
              border: '1px solid #ddd',
              margin: '5px 0',
            }}
            pullToRefresh={<PullToRefresh
              refreshing={this.state.refreshing}
              onRefresh={this.onRefresh}
            />}
            onEndReached={this.onEndReached}
            pageSize={5}
          />
        }
      </>
    )
  }
}

export default MySponsor
