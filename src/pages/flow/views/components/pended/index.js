import React, { PureComponent } from 'react'
import ReactD<PERSON> from 'react-dom'
import { PullToRefresh, Result, ListView, Toast } from 'antd-mobile'
import { SwipeAction, Modal } from 'antd-mobile-v5'
import API from '../apis'
import './index.less'
import { cloneDeep, isEmpty } from 'lodash'
import {
  AGREE, RECALL, COUNTERSIGN_NEXT, APPROVALING,
  ENDORSE, COUNTERSIGN_PRE, COUNTERSIGN_NEXT_MINUS_SIGN,
  RECAPTION, COUNTERSIGN_PRE_MINUS_SIGN, ENDORSE_MINUS_SIGN,
  ROLLBACK_INITIATOR, DRAFT
} from '../../constants/approve'

let searchValue
class Pended extends PureComponent {
  constructor(props) {
    super(props);
    const dataSource = new ListView.DataSource({
      rowHasChanged: (row1, row2) => row1 !== row2,
    });
    this.state = {
      dataSource,
      statusObj: {
        0: '待审批',
        1: '审批中',
        2: '已审批',
        3: '驳回',
        4: '撤回',
        5: '草稿'
      },
      searchParams: {
        page: 1,
        page_size: 50
      },
      NowPage: 1,
      total: 0,
      IsGoReq: 0,
      flag: false,
      damping: 60,
      refreshing: true,
      isLoading: true,
      height: document.documentElement.clientHeight,
      hasMore: true,
      ifEmpty: false
    };
    this.pageIndex = 1
    this.props.onRef(this)
  }

  componentDidUpdate () {
    document.body.style.overflow = 'hidden'
  }

  enterChange = (value) => {
    const dataSource = new ListView.DataSource({
      rowHasChanged: (row1, row2) => row1 !== row2,
    })
    Toast.loading('Loading...', 0)
    searchValue = value
    this.setState({
      dataSource: dataSource.cloneWithRows([]),
      searchParams: {
        page: 1,
        page_size: 50,
      }
    }, async function() {
      this.pageIndex = 1
      const data = await this.getApprovedList()
      this.rData = data.data.task
      data.data.task.forEach(item => {
        item.status = item.process?.status
        if (item.status === 0 || item.status === 1) {
          item.colorValue = '#F19725'
        } else if (item.status === 2) {
          item.colorValue = '#40B335'
        } else if (item.status === 3) {
          item.colorValue = '#EF3D3D'
        } else {
          item.colorValue = '#B7B7B7'
        }
      })
      // const dataSource = new ListView.DataSource({
      //   rowHasChanged: (row1, row2) => row1 !== row2,
      // });
      this.setState({
        dataSource: dataSource.cloneWithRows(data.data.task),
      }, () => {
        Toast.hide()
      });
    })
  }

  componentDidMount() {
    const hei = this.state.height - ReactDOM.findDOMNode(this.lv).offsetTop - document.getElementsByClassName('pending-search-wrapper')[0].offsetHeight
    setTimeout(async () => {
      const data = await this.getApprovedList()
      this.rData = data?.data?.task || []
      data && data.data.task.forEach(item => {
        // item.status = item.status
        if (item.status === 0 || item.status === 1) {
          item.colorValue = '#F19725'
        } else if (item.status === 2) {
          item.colorValue = '#40B335'
        } else if (item.status === 3) {
          item.colorValue = '#EF3D3D'
        } else {
          item.colorValue = '#B7B7B7'
        }
      })
      this.setState({
        dataSource: this.state.dataSource.cloneWithRows(this.rData),
        height: hei,
        refreshing: false,
        isLoading: false,
        ...(this.rData.length ? {} : { ifEmpty: true })
      });
    }, 1000)
  }

  handleApprovedList = () => {
    setTimeout(async () => {
      this.pageIndex = 1
      const data = await this.getApprovedList()
      this.rData = data.data.task
      data.data.task.forEach(item => {
        if (item.status === 0 || item.status === 1) {
          item.colorValue = '#F19725'
        } else if (item.status === 2) {
          item.colorValue = '#40B335'
        } else if (item.status === 3) {
          item.colorValue = '#EF3D3D'
        } else {
          item.colorValue = '#B7B7B7'
        }
      })
      this.setState({
        dataSource: this.state.dataSource.cloneWithRows(data.data.task),
        refreshing: false,
        isLoading: false,
      });
    }, 600);
  }

  /**
   * 获取我的流程列表
   */
  getApprovedList = (flag) => {
    const { searchParams, myFlowData } = this.state
    const tempParams = searchParams
    if (searchValue && searchValue.indexOf('@inke.cn') !== -1) {
      tempParams.user = searchValue
    } else if (searchValue && searchValue.indexOf('@inke.cn') === -1) {
      tempParams.name = searchValue
    } else {
      delete tempParams.name
      delete tempParams.user
    }
    return API.getApprovedList({
      ...tempParams,
      page: this.pageIndex
    }).catch(err => {
      Toast.fail(err.msg, 1);
    })
  }

  /**
   * 点击展示深审批详情
   * @param { 详情要展示数据 } data
   */
  handleDetailFun = (data) => {
    this.props.history.push({
      pathname: `/contract/approval/list`,
      search: `?id=${data.taskId}&pending=false`
    })
  }

  onRefresh = () => {
    this.setState({ refreshing: true, isLoading: true });
    setTimeout(async () => {
      this.pageIndex = 1
      const data = await this.getApprovedList()
      this.rData = data.data.task
      data.data.task.forEach(item => {
        if (item.status === 0 || item.status === 1) {
          item.colorValue = '#F19725'
        } else if (item.status === 2) {
          item.colorValue = '#40B335'
        } else if (item.status === 3) {
          item.colorValue = '#EF3D3D'
        } else {
          item.colorValue = '#B7B7B7'
        }
      })
      this.setState({
        dataSource: this.state.dataSource.cloneWithRows(data.data.task),
        refreshing: false,
        isLoading: false,
      });
    }, 600);
  }

  onEndReached = (event) => {
    // load new data
    // hasMore: from backend data, indicates whether it is the last page, here is false
    if (this.state.isLoading && !this.state.hasMore) {
      return;
    }
    this.setState({ isLoading: true });
    setTimeout(async () => {
      ++this.pageIndex
      const data = await this.getApprovedList()
      this.rData = this.rData.concat(data.data.task)
      if (!data.data.task.length) {
        this.setState({
          isLoading: false,
          hasMore: false
        })
        return
      }
      data.data.task && data.data.task.forEach(item => {
        if (item.status === 0 || item.status === 1) {
          item.colorValue = '#F19725'
        } else if (item.status === 2) {
          item.colorValue = '#40B335'
        } else if (item.status === 3) {
          item.colorValue = '#EF3D3D'
        } else {
          item.colorValue = '#B7B7B7'
        }
      })
      this.setState({
        dataSource: this.state.dataSource.cloneWithRows(this.rData),
        isLoading: false,
      });
    }, 1000);
  }

  renderDom = (status, nextApprover) => {
    if (status === 2) {
      return <span style={{ color: '#40B335' }}>已结束</span>
    } else if (status === 3) {
      return <span style={{ color: '#EF3D3D' }}>驳回</span>
    } else if (nextApprover && nextApprover.length) {
      return <span style={{ color: '#000' }}>
        <span>待办：</span>
        <span style={{ color: '#F19725' }}>{nextApprover[0].real_name}</span>
      </span>
    } else {
      return <span style={{ color: '#40B335' }}>已结束</span>
    }
  }

  /**
   * 取回/减签接口
   * @param { Object } data 数据对象
   * @param { Number } type 取回/减签类型，4:取回, 5:转签减签, 6:加签前减签, 7:加签后减签,
   * @param { String } okMsg 操作成功提示
   */
  flowRetrieve = (data, type, okMsg) => {
    API.flowRetrieve({
      form_id: data.taskId,
      take_back_type: type
    }).then(res => {
      Toast.success(okMsg || '操作成功！', 1)
      this.setState({ refreshing: true, isLoading: true })
      this.handleApprovedList()
    }).catch(err => {
      Toast.fail(err.msg)
    })
  }

  /**
   * 取回
   * @param { Object } data 当前操作数据
   * @param { Number } backType 回退类型，4取回，5转签减签，6加签前减签，7加签后减签
   */
  handleRetrieve = (data, backType) => {
    Modal.alert({
      title: '取回确认',
      content: `您确定要取回流程 ${data.name} 吗？`,
      showCloseButton: true,
      onConfirm: () => {
        this.flowRetrieve(data, backType, '取回成功！')
      },
    })
  }

  /**
   * 减签
   * @param { Object } data 当前操作数据
   * @param { Number } backType 减签类型，4取回，5转签减签，6加签前减签，7加签后减签
   */
  handleMinuSign = (data, backType) => {
    const peopleArr = !isEmpty(data?.nextApprover) ? data.nextApprover.map(item => item.real_name) : []
    Modal.alert({
      title: '减签确认',
      content: `您确定要减签临时审批人 ${peopleArr.toString()} 吗？`,
      showCloseButton: true,
      onConfirm: () => {
        this.flowRetrieve(data, backType, '减签成功！')
      },
    })
  }

  // 右滑已处理列表操作
  rightActions = (dataObj) => {
    switch (dataObj.status) {
      case AGREE: // 2同意
      case RECALL: // 4撤回
      case DRAFT: // 5草稿
      case ROLLBACK_INITIATOR: // 9回退至发起人
        return []
      case COUNTERSIGN_NEXT: // 8加签-我之后，对应取回(减签)类型:7加签后减签
      case APPROVALING: // 1审批中, is_countersign是否加签，两条件同时满足表示加签-我之后
        return [{
          key: 'recaption',
          text: '取回',
          color: 'danger',
          onClick: this.handleRetrieve.bind(this, dataObj, dataObj.is_countersign ? COUNTERSIGN_NEXT_MINUS_SIGN : RECAPTION)
        }]
      case ENDORSE: // 11转签，对应取回(减签)类型:5转签减签
      case COUNTERSIGN_PRE: // 12加签-我之前，对应取回(减签)类型:6加签前减签
        return [{
          key: 'minus_sign',
          text: '减签',
          color: 'danger',
          onClick: this.handleMinuSign.bind(this, dataObj, dataObj.status === ENDORSE ? ENDORSE_MINUS_SIGN : COUNTERSIGN_PRE_MINUS_SIGN)
        }]
      default: // 取回(减签)类型:4取回
        return [{
          key: 'recaption',
          text: '取回',
          color: 'danger',
          onClick: this.handleRetrieve.bind(this, dataObj, RECAPTION)
        }]
    }
  }

  render () {
    const { dataSource, damping, ifEmpty } = this.state
    const row = (rowData, sectionID, rowID) => {
      const obj = cloneDeep(rowData)
      return (
        <div
          key={rowID}
          className='mySponsor-swiper'
        >
          <SwipeAction
            key={rowID}
            rightActions={this.rightActions(rowData)}
            className='swipe-content'
          >
            <div className='mySponsor-li' onClick={this.handleDetailFun.bind(this, obj)}>
              <span className='content-top'>
                <span className='content-arrow-text'>{obj.name}</span>
                <span className='content-arrow-icon'></span>
              </span>
              <span className='content-cent'>
                <span>发起时间：{obj.createdAt}</span>
                <span>更新时间：{obj.updatedAt}</span>
              </span>
              <span className='content-bottom'>
                {
                  this.renderDom(obj.status, obj.nextApprover)
                }
              </span>
            </div>
          </SwipeAction>
        </div>
      )
    }

    return (
      <>
        {/* <Search enterChange={this.enterChange}></Search> */}
        {
          ifEmpty ? <div className='empty-result'><Result
            img={<img src='https://img.ikstatic.cn/MTYxNDgyNzkzMzQ5NyMxODUjcG5n.png' alt='空' />}
            message="暂无数据"
          /></div> : <ListView
            className='pended-list-view-wrapper'
            key={'1'}
            ref={(ref) => {
              if (ref) {
                this.lv = ref
              }
            }}
            dataSource={dataSource}
            renderFooter={() => (<div className='loading-bottom'>
              {this.state.isLoading ? 'Loading...' : '没有更多'}
            </div>)}
            renderRow={row}
            useBodyScroll={false}
            style={{
              height: this.state.height,
              border: '1px solid #ddd',
              margin: '5px 0',
            }}
            pullToRefresh={<PullToRefresh
              refreshing={this.state.refreshing}
              onRefresh={this.onRefresh}
            />}
            onEndReached={this.onEndReached}
            pageSize={5}
          />
        }
      </>
    )
  }
}

export default Pended
