.pending-search-wrapper {
  height: 100px;
  width: 750px;
  background-color: #fff;
  .pending-search-wrapper-content {
    background-color: #fff;
    height: 100px;
    width: 750px;
    position: fixed;
    top: 0;
    z-index: 9999;
  }
  .pending-search {
    width: 750px;
    text-align: center;
    position: fixed;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    // background: #fff;
    top: 0;
    z-index: 232;
    .pending-search-input {
      border: 2px solid #dfdfdf;
      width: 90%;
      border-radius: 50px;
      height: 70px;
      padding-left: 80px;
      font-size: 36px;
      color: #000;
    }
    input::-webkit-input-placeholder  {
      color: rgba(0, 0, 0, 0.65);
      font-size: 30px;
    }
    .pending-search-icon {
      position: absolute;
      top: 34px;
      left: 62px;
    }
  }
  .pending-search2 {
    width: 650px;
  }
  .show-switch-btn {
    position: relative;
    left: 620px;
    top: 20px;
  }
}

