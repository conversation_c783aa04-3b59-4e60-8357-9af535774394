/* eslint-disable react-hooks/exhaustive-deps */
import React, { memo, useEffect, useState, useImperativeHandle, forwardRef } from 'react'
import { Icon, Modal, Button, Toast } from 'antd-mobile'
import './index.less'
// let searchValue
const prompt = Modal.prompt
const Search = (props, ref) => {
  const [showSwitch, setShowSwitch] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  useEffect(() => {
    if (window.location.hostname === 'localhost' || /^\d(.*\d)?$/.test(window.location.hostname)) {
      setShowSwitch(true)
    } else {
      setShowSwitch(false)
    }
  }, [])

  useImperativeHandle(ref, () => ({
    clearValue: () => {
      setSearchValue('')
    }
  }))

  const search = (event) => {
    if (event.keyCode === 13 && typeof props.enterChange === 'function') {
      // this.getPendingApproveList()
      props.enterChange(searchValue)
    }
  }

  const inputValue = (e) => {
    setSearchValue(e.target.value)
  }

  const switchUser = (value) => {
    if (!value) {
      Toast.fail('用户邮箱不能为空', 1)
      return
    }
    window.localStorage.setItem('mail', value)
    window.localStorage.setItem('Auth-Type', 'custom')
    setTimeout(() => {
      window.location.reload()
    }, 1000)
  }

  return <div className='pending-search-wrapper'>
    <div className='pending-search-wrapper-content'>
      <div className={showSwitch ? 'pending-search pending-search2' : 'pending-search'}>
        <Icon type="search" size='xs' className='pending-search-icon' />
        <input type='text' className='pending-search-input' value={searchValue} placeholder='可搜索标题、编号、申请人(完整邮箱)' onChange={inputValue} onKeyDown={search} />
      </div>
      {
        showSwitch ? <div className='show-switch-btn'>
          <Button style={{ width: 60, border: 'none' }} size='small' onClick={() => prompt('用户切换', '', [
            { text: '取消' },
            { text: '确定', onPress: value => switchUser(value) },
          ], 'default', '')}
          >切换</Button>
        </div> : ''
      }
    </div>
  </div>
}
export default memo(forwardRef(Search))
