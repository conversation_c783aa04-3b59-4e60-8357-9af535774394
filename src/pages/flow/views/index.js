/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-23 14:27:28
 * @LastEditTime: 2020-11-23 20:28:47
 * @LastEditors: Please set LastEditors
 * @Description: 流程审批
 */
import React, { PureComponent } from 'react'
// import NavTopBar from 'components/NavTopBar'
import setTitle from 'components/setBarTitle'
import './index.less'

class Flow extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      urlName: window.location.origin + '/home/<USER>',
      itemDatasList: [
        {
          title: '最近使用',
          result: [
            {
              icon: require('assets/flow/contract-icon.png'),
              name: '合同',
              type: 'contract'
            }
          ]
        }
      ]
    };
  }

  /**
   * 点击不同流程
   * @param { 类别 } type
   */
  handleFlowFun = (type) => {
    if (type === 'contract') {
      this.props.history.push('/' + type)
    }
  }

  render () {
    const { itemDatasList } = this.state
    return (
      <div className='flow'>
        {/* <NavTopBar title='流程审批' JumpUrl={this.state.urlName} /> */}
        <div className='flow-banner'></div>
        <ul className='flow-content'>
          {
            itemDatasList.map((item, index) => {
              return <li className='flow-content-li' key={index}>
                <h3 className='flow-li-title'>{item.title}</h3>
                <div className='flow-list'>
                  {
                    item.result.map((ele, j) => {
                      return <div className='flow-every-ele' key={j} onClick={this.handleFlowFun.bind(this, ele.type)}>
                        <img alt='contract' src={ele.icon} className='ele-icon' />
                        <span>{ele.name}</span>
                      </div>
                    })
                  }
                </div>
              </li>
            })
          }
        </ul>
      </div>
    )
  }
}

export default Flow
