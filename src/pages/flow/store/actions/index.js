import { ALL_PEOPLE } from '../constants'
import API from '../../views/components/apis'

// 保存所有人
export const saveAllPeople = (payload) => {
  return {
    type: ALL_PEOPLE,
    payload
  }
}

export const getAllPeople = (payload = {}) => async (dispatch, getState) => {
  API.getAllPeople().then(res => {
    const data = res.data || []
    if (data.length) {
      dispatch(saveAllPeople(res.data))
    }
  }).catch(err => {
    console.log(err)
  })
}
