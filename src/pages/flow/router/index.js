import React, { Suspense, lazy } from 'react'
import { <PERSON>, Switch, <PERSON>h<PERSON><PERSON><PERSON>, Brows<PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import LoadingView from 'components/_loading'

const Router = BrowserRouter

// https://reactjs.org/docs/code-splitting.html#reactlazy

// const Flow = lazy(() => import('../views'))
const FlowManagement = lazy(() => import('../views/components/index'))
const InitiateProcess = lazy(() => import('../views/components/ContractManagement/components/InitiateProcess'))
const ApprovalDetail = lazy(() => import('../views/components/mySponsor/components/ApprovalDetail'))
const ApprovalDetailList = lazy(() => import('../views/components/mySponsor/components/detailList'))
const BackDetail = lazy(() => import('../views/components/mySponsor/components/ApprovalDetail/backCompoent'))
const Contract = lazy(() => import('../views/components/index'))
const CounterSign = lazy(() => import('../views/components/mySponsor/components/ApprovalDetail/counterSign'))

const linkStyle = {
  textDecoration: 'underline',
  margin: 10
}

const getRouter = _ => (
  <HashRouter>
    <Suspense fallback={<LoadingView />}>
      {/* 异步引入 */}
      {/* <Route exact path="/" render={props => <Flow {...props}/>} /> */}
      {/* <Route exact path="/:type" render={props => <FlowManagement {...props}/>} /> */}
      <Route exact path="/contract" render={props => <FlowManagement {...props}/>} />
      <Route exact path="/:type/:id" render={props => <InitiateProcess {...props} />} />
      <Route exact path='/:type/approval/list' render={props => <ApprovalDetail {...props} />} />
      <Route exact path='/:type/approvalList/detail' render={props => <ApprovalDetailList {...props} />} />
      <Route exact path='/backDetail' render={ props => <BackDetail {...props} /> } />
      <Route exact path='/contract/counterSign/content' render={ props => <CounterSign {...props} /> } />
      <Route exact path='/:type/contract/:id' render={props => <Contract {...props}></Contract>}></Route>
    </Suspense>
  </HashRouter>
)

export default getRouter
