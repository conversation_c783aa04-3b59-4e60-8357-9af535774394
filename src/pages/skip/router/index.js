import React, { Suspense, lazy } from 'react'
import { Route, HashRouter } from 'react-router-dom'
import LoadingView from 'components/_loading'
// const Router = BrowserRouter

// https://reactjs.org/docs/code-splitting.html#reactlazy

const Index = lazy(() => import('../views'))
// const Motion = lazy(() => import('../views/Motion'))
// const ReactHooks = lazy(() => import('../views/ReactHooks'))

const linkStyle = {
  textDecoration: 'underline',
  margin: 10
}

const getRouter = _ => (
  <HashRouter>
    <Suspense fallback={<LoadingView />}>
      {/* 异步引入 */}
      <Route exact path="/skip" render={props => <Index {...props}/>} />
    </Suspense>
  </HashRouter>
)

export default getRouter
