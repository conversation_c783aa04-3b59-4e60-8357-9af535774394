import React, { PureComponent } from 'react'
import util from 'utils'
import './home.less'

class Skip extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {}
  }
  componentDidMount() {
    const param = util.getUrlParams(window.location.href)
    const {
      domain,
      routerEntry
    } = param
    delete param.domain
    delete param.routerEntry
    const paramStr = util.stringifyParams(param)
    if (util.ua.pc) {
      window.location.href = `https://${domain}${routerEntry}?${paramStr}`
    } else {
      window.location.href = `https://mobile-family.ikbase.cn/flow/index.html#/contract/approval/list?id=${param.id}&pending=true`
    }
  }

  render () {
    return <div className='skip-wrapper'>跳转页面</div>
  }
}
export default Skip
