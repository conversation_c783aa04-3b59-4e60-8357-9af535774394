/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-03 14:27:28
 * @LastEditTime: 2020-11-20 20:28:47
 * @LastEditors: Please set LastEditors
 * @Description: 订餐
 */
import React, { PureComponent } from 'react'
import { TabBar } from 'antd-mobile'
import './index.less'

import FirstPage from './components/FirstPage'
import MyAppointment from './components/MyAppointment'

class Order extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      selectedTab: 'visitorPage',
      bottomNavBar: [
        {
          title: '首页',
          key: 'visitorPage',
          url: '#/'
        },
        {
          title: '我的预约',
          key: 'myAppointment',
          url: '#/?tab=myappoint'
        }
      ],
      valueId: 0,
      flag: 0
    };
  }

  // renderContent(pageText, index) {
  //   switch (index) {
  //     case 0:
  //       return <FirstPage ref='FirstPage' {...this.props} />
  //     case 1:
  //       return <MyAppointment ref='MyAppointment' />
  //     default:
  //       break
  //   }
  // }

  componentDidMount () {
    let urlHashValue = window.location.hash
    switch (urlHashValue) {
      case '#/':
        this.setState({
          valueId: 0,
          selectedTab: 'visitorPage'
        })
        break;
      case '#/?tab=myappoint':
        this.setState({
          valueId: 1,
          selectedTab: 'myAppointment'
        })
        break;
      default:
        this.setState({
          valueId: 0,
        })
        break;
    }
  }

  /**
   * tabbar点击回调函数
   * @param { 当前tabbar下标 } e
   * @param { 当前tabbar相关参数 } item
   */
  handleNavBarBottomFun = (e, item) => {
    console.log('nnnnnnnn', e, this.state.flag)
    // if (this.state.flag !== e) {
    //   switch (e) {
    //     case 0:
    //       this.setState({
    //         valueId: 0,
    //         selectedTab: 'visitorPage'
    //       })
    //       window.location.href = window.location.origin + window.location.pathname + item.url
    //       break
    //     case 1:
    //       this.setState({
    //         valueId: 1,
    //         selectedTab: 'myAppointment'
    //       })
    //       if (this.refs.MyAppointment) this.refs.MyAppointment.receive()
    //       window.location.href = window.location.origin + window.location.pathname + item.url
    //       break
    //     default:
    //       break
    //   }
    // }
    switch (e) {
      case 0:
        this.setState({
          valueId: 0,
          selectedTab: 'visitorPage'
        })
        window.location.href = window.location.origin + window.location.pathname + item.url
        break
      case 1:
        if (this.state.flag !== e) {
          this.setState({
            valueId: 1,
            selectedTab: 'myAppointment'
          })
          if (this.refs.MyAppointment) this.refs.MyAppointment.receive()
          window.location.href = window.location.origin + window.location.pathname + item.url
        }
        break
      default:
        break
    }
  }

  // 回到首页
  backNowFirstPage = () => {
    this.setState({
      valueId: 0,
      selectedTab: 'visitorPage',
      flag: 0
    })
    window.location.href = window.location.origin + window.location.pathname + '#/'
  }

  render () {
    const { bottomNavBar, selectedTab } = this.state
    return (
      <div className='visitor'>
        <TabBar
          unselectedTintColor="#000000"
          tintColor="#000000"
          barTintColor="#FFFFFF"
          tabBarPosition='bottom'
          className='visitor-tabBar'
          prerenderingSiblingsNumber={0}
        >
          {
            bottomNavBar.map((item, index) => {
              return (
                <TabBar.Item
                  title={item.title}
                  key={item.key}
                  icon={<div className={item.key} />}
                  selectedIcon={<div className={`${item.key}-active`} />}
                  selected={selectedTab === item.key}
                  onPress={() => {
                    this.setState({
                      selectedTab: item.key,
                      flag: index
                    })
                    this.handleNavBarBottomFun(index, item)
                  }}
                >
                  {
                    this.state.valueId === 0 ? <FirstPage ref='FirstPage' {...this.props} backNowFirstPage={this.backNowFirstPage} /> : ''
                  }
                  {
                    this.state.valueId === 1 ? <MyAppointment ref='MyAppointment' {...this.props} backNowFirstPage={this.backNowFirstPage} /> : ''
                  }
                </TabBar.Item>
              )
            })
          }
        </TabBar>
      </div>
    )
  }
}

export default Order
