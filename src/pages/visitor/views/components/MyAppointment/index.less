.visitor-appointment{
  // height: calc(100vh - 88px - 108px);
  // overflow-y: scroll;
  .every-swiper{
    box-shadow: 0px -1px 0px 0px #E8E8E8;
    background: #FFFFFF;
    padding-bottom: 2px;
    overflow: hidden;
    position: relative;
    z-index: 10000;
  }
  .content{
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
    background: #FFFFFF;
    width: 690px;
    height: 237px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999999;
    margin: 0 auto;
    .appoint-top{
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      line-height: 60px;
      span:nth-child(1) {
        font-size: 34px;
        font-weight: 500;
        color: #000000;
      }
    }
    .appoint-center{
      display: flex;
      flex-direction: column;
      span{
        line-height: 40px;
      }
    }
    .appoint-bottom{
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      line-height: 46px;
      span:nth-child(1) {
        width: 530px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
      span:nth-child(2) {
        font-size: 26px;
        font-weight: 500;
        color: #F19725;
      }
    }
  }
  .visitor-calendar {
    text-align: 'center';
    background: '#ccc';
    height: 739px;
  }
  .visitor-divider {
    height: 20px;
    background-color: #F0F5F5;
  }
}
.visitor-reject, .visitor-resolve{
  width: 156px;
  font-size: 30px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
}
.visitor-reject{
  background: #EF3D3D;
}
.visitor-resolve{
  background: #40B335;
}
.am-pull-to-refresh-content-wrapper{
  min-height: 100%;
  // overflow-y: scroll;
}
.am-pull-to-refresh-indicator{
  line-height: 110px;
  display: block;
}