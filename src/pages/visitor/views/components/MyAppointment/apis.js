/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-17 11:00:46
 * @LastEditTime: 2020-11-17 11:18:05
*/

import { getRequestsByRoot } from 'axios-service'
import srcConfig from 'src/config/apiRoots'

const { get, post, postXForm } = getRequestsByRoot({ root: srcConfig.familyRoot })
class Apis {
  /**
   * 获取访客信息列表
   */
  getVisitorsLists = get('api/v2/visitor/queryme', {}, { autoLoading: false })

  /**
   * 接口：签到
   */
  getVisitorSignIn = get('api/v2/visitor/signin', {}, { autoLoading: false })

  /**
   * 接口：取消预约
   */
  getCancelLation = get('api/v2/visitor/cancel', {}, { autoLoading: false })
}

export default new Apis()
