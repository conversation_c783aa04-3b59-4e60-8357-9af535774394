import React, { Component } from 'react'
import ReactDOM from 'react-dom'
import CalendarComponent from '../calendarComponent'
import API from './apis'
import './index.less'
import { PullToRefresh, SwipeAction } from 'antd-mobile';
import NavTopBar from 'components/NavTopBar'

class ListContainer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      refreshing: false,
      down: false,
      height: document.documentElement.clientHeight,
      data: [],
      searchParams: {
        start_time: '',
        end_time: '',
        num: 20,
        page: 1
      },
      visitorAppointmentList: [],
      numData: 0,
      mark: false
    };
  }

  componentDidMount() {
    this.getVisitorsLists()
  }

  receive = () => {
    this.setState({
      searchParams: {
        start_time: '',
        end_time: '',
        num: 20,
        page: 1,
      },
      mark: false
    }, () => {
      this.getVisitorsLists(2)
    })
  }

  /**
   * 获取访客预约列表数据
   * @param { 1: 签到或者取消，2: 时间选择查询 || 父组件传值， false: 第一次进入或者上拉刷新} flag
   */
  getVisitorsLists = (flag) => {
    const { searchParams } = this.state
    API.getVisitorsLists(searchParams).then(res => {
      res.data.Visitors.length && res.data.Visitors.forEach(item => {
        item.createTime = item.create_time.split(' ')[0]
        if (item.flag === '已签到') {
          item.color = '#40B335'
        } else if (item.flag === '未签到') {
          item.signin_time = '-'
          item.color = '#F19725'
        } else {
          item.signin_time = '-'
          item.color = '#EF3D3D'
        }
      })
      const isTrue = res.data.Visitors ? res.data.Totals - (this.state.searchParams.page * searchParams.num) : 0
      if (isTrue > 0) {
        this.setState({
          damping: 60,
          mark: false
        })
      } else {
        this.setState({
          damping: 50,
          mark: true
        })
      }
      document.querySelector('.visitor-refresh .am-pull-to-refresh-indicator').style.display = 'block'
      if (flag === 1) {
        const { visitorAppointmentList, searchParams } = this.state
        const arr = []
        visitorAppointmentList.forEach((item, i) => {
          if (i === searchParams.page - 1) {
            arr.push(res.data.Visitors)
          } else {
            arr.push(item)
          }
        })
        this.setState({
          visitorAppointmentList: arr,
          refreshing: false,
          isLoading: false,
          numData: res.data.Visitors.length
        })
      } else if (flag === 2) {
        const arr = []
        arr.push(res.data.Visitors)
        // visitorAppointmentList.push(res.data.Visitors)
        this.setState({
          visitorAppointmentList: arr,
          refreshing: false,
          isLoading: false,
          numData: res.data.Visitors.length
        }, () => {
          const myComp = this.refs.RefVisitor
          const dom = ReactDOM.findDOMNode(myComp)
          if (dom) {
            if (dom.clientHeight < document.querySelector('.visitor-refresh').scrollHeight) {
              this.setState({
                damping: -10
              })
              document.querySelector('.visitor-refresh .am-pull-to-refresh-indicator').style.display = 'none'
            }
          }
        })
      } else {
        const { visitorAppointmentList } = this.state
        visitorAppointmentList.push(res.data.Visitors)
        this.setState({
          visitorAppointmentList: visitorAppointmentList,
          refreshing: false,
          isLoading: false,
          numData: res.data.Visitors.length
        })
      }
    }).catch(err => {
      console.log(err)
    })
  }

  // 签到接口
  getVisitorSignIn = (id, index) => {
    API.getVisitorSignIn({
      id
    }).then(res => {
      console.log(res)
      const { searchParams, numData } = this.state
      this.setState({
        searchParams: {
          ...searchParams,
          page: index + 1
        }
      }, () => {
        this.getVisitorsLists(1)
      })
    }).catch(err => {
      console.log(err)
    })
  }

  // 取消接口
  getCancelLation = (id, index) => {
    API.getCancelLation({
      id
    }).then(res => {
      const { searchParams, numData } = this.state
      this.setState({
        searchParams: {
          ...searchParams,
          page: index + 1
        }
      }, () => {
        this.getVisitorsLists(1)
      })
    }).catch(err => {
      console.log(err)
    })
  }

  /**
   * 刷新回调函数
   */
  onRefresh = () => {
    const { searchParams, numData } = this.state
    if (numData === searchParams.num) {
      this.setState({
        searchParams: {
          ...searchParams,
          page: searchParams.page + 1
        }
      }, () => {
        this.getVisitorsLists()
      })
    }
  };

  /**
   * 点击通过（签到）
   * @param { id } id
   * @param { 下标 } index
   */
  SignInVisitor = (id, index) => {
    this.getVisitorSignIn(id, index)
  }

  /**
   * 点击驳回（取消）
   * @param { id } id
   * @param { 下标 } index
   */
  cancelLationVisitor = (id, index) => {
    this.getCancelLation(id, index)
  }

  /**
   * 时间改变查询
   * @param { 时间 } time
   * @param { boolean，true:点击回到今天查询所有，false:查询指定日期 } value
   */
  handleTimeChange = (time, value) => {
    console.log(value, 'qiqiqiqiq', this.state.searchParams)
    if (value) {
      this.setState({
        searchParams: {
          ...this.state.searchParams,
          start_time: '',
          end_time: '',
          page: 1,
        }
      }, () => {
        this.getVisitorsLists()
      })
    } else {
      this.setState({
        searchParams: {
          ...this.state.searchParams,
          page: 1,
          start_time: new Date(time + ' 00:00:00').format('yyyy-MM-dd hh:mm:ss'),
          end_time: new Date(time + ' 23:59:59').format('yyyy-MM-dd hh:mm:ss')
        }
      }, () => {
        this.getVisitorsLists(2)
      })
    }
  }

  render() {
    const { visitorAppointmentList, numData, searchParams, damping } = this.state
    return (<div>
      <div className='nav'>
        <NavTopBar title='我的预约' {...this.props} BackHomePage={true} />
      </div>
      <PullToRefresh
        damping={damping}
        className='visitor-refresh'
        // indicator={{}}
        // direction={numData === searchParams.num ? 'up' : 'down'}
        direction={'up'}
        refreshing={this.state.refreshing}
        distanceToRefresh={25}
        indicator={
          this.state.mark ? { activate: '到底啦～', deactivate: '到底啦～', release: '到底啦～', finish: '到底啦～' } : {}
        }
        onRefresh={() => {
          this.onRefresh()
        }}
      >
        {
          <div className='visitor-appointment' ref='RefVisitor'>
            <div className='visitor-calendar'>
              <CalendarComponent
                handleTimeChange={this.handleTimeChange}
              />
            </div>
            <div className='visitor-divider'></div>
            {
              visitorAppointmentList.map((ele, index) => {
                return ele.map((item, i) => {
                  if (item.flag === '未签到') {
                    return <SwipeAction
                      className='every-swiper'
                      autoClose
                      right={[
                        {
                          className: 'visitor-reject',
                          text: '驳回',
                          onPress: () => this.cancelLationVisitor(item.id, index),
                        },
                        {
                          className: 'visitor-resolve',
                          text: '通过',
                          onPress: () => this.SignInVisitor(item.id, index),
                        },
                      ]}
                      key={item.id}
                    >
                      <div className='content'>
                        <div className='appoint-top'>
                          <span>{item.name}</span>
                          <span>{item.createTime}</span>
                        </div>
                        <div className='appoint-center'>
                          <span>到访时间：{item.start_time}</span>
                          <span>签到时间：{item.signin_time}</span>
                        </div>
                        <div className='appoint-bottom'>
                          <span>接待人：{item.receiver}</span>
                          <span>{item.flag}</span>
                        </div>
                      </div>
                    </SwipeAction>
                  } else {
                    return <div
                      className='every-swiper'
                      key={item.id}
                    >
                      <div className='content'>
                        <div className='appoint-top'>
                          <span>{item.name}</span>
                          <span>{item.createTime}</span>
                        </div>
                        <div className='appoint-center'>
                          <span>到访时间：{item.start_time}</span>
                          <span>签到时间：{item.signin_time}</span>
                        </div>
                        <div className='appoint-bottom'>
                          <span>接待人：{item.receiver}</span>
                          <span style={{
                            color: item.color
                          }}>{item.flag}</span>
                        </div>
                      </div>
                    </div>
                  }
                })
              })
            }
          </div>
        }
      </PullToRefresh>
    </div>);
  }
}

export default ListContainer
