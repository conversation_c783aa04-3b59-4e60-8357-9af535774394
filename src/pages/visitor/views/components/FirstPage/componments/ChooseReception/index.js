/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-19 10:05:20
 * @LastEditTime: 2020-11-19 19:36:00
 * @LastEditors: Please set LastEditors
 * @Description: 发起预约
 */
import React, { PureComponent } from 'react'
import { SearchBar } from 'antd-mobile';
import './index.less'
class ChooseReception extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      AllNameLists: [],
      nameLists: [],
      userId: undefined,
      userName: ''
    }
  }

  /**
   * 接受父组件函数
   * @param { 父组件传递过来的数据 } data
   */
  receiver = (data) => {
    const arr = data.length > 100 ? data.slice(0, 100) : data
    this.setState({
      AllNameLists: data,
      nameLists: arr
    })
  }

  handlefocus = () => {
    setTimeout(() => {
      this.autoFocusInst.focus();
    }, 0);
  }

  // 点击取消
  receptionCancal = () => {
    this.props.receptionCancal()
  }

  // 点击确认
  confirmOK = () => {
    this.props.confirmOK(this.state.userName)
  }

  // 搜索框值改变回调函数
  SearchChange = (val) => {
    const { AllNameLists, nameLists } = this.state
    if (val) {
      const arr = []
      AllNameLists.length && AllNameLists.forEach(item => {
        if ((item.realname.indexOf(val) !== -1 || item.username.indexOf(val) !== -1) && arr.length < 30) {
          arr.push(item)
        }
      })
      this.setState({
        nameLists: arr
      })
    } else {
      this.setState({
        nameLists: AllNameLists.length > 100 ? AllNameLists.slice(0, 100) : AllNameLists
      })
    }
  }

  /**
   * 点击想要选中的人回调函数
   * @param { 当前选中的信息 } item
   */
  selectedName = (item) => {
    this.setState({
      userId: item.user_id,
      userName: item.realname
    })
  }

  render() {
    const { nameLists } = this.state
    return <div className='reception'>
      <div className='header'>
        <span onClick={() => this.receptionCancal()}>取消</span>
        <span>选择接待人</span>
        <span onClick={() => this.confirmOK()}>完成</span>
      </div>
      <div className='reception-content'>
        <SearchBar
          className='search'
          placeholder="请输入姓名"
          ref={ref => (this.autoFocusInst = ref)}
          onChange={(val) => this.SearchChange(val)}
        />
        <ul className='reception-content-ul'>
          {
            nameLists.map((item, index) => {
              return <li className={this.state.userId === item.user_id ? 'reception-content-li reception-content-li-active' : 'reception-content-li'} key={item.user_id} onClick={() => this.selectedName(item)}>
                {item.realname}
              </li>
            })
          }
        </ul>
      </div>
    </div>
  }
}

export default ChooseReception
