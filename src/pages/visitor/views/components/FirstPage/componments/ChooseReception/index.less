.reception{
  height: 100vh;
  width: 100vw;
  background: #FFFFFF;
  overflow: scroll;
  .header{
    width: 750px;
    height: 88px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-size: 36px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 500;
    span:nth-child(1) {
      color: #666666;
    }
    span:nth-child(2) {
      color: #000000;
    }
    span:nth-child(3) {
      color: #00C8B7;
    }
  }
  .reception-content{
    .search{
      width: 670px;
      height: 88px;
      background: #F2F2F2;
      border-radius: 10px;
      margin: 16px auto;
      font-size: 30px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
    }
    .reception-content-ul{
      .reception-content-li{
        width: 670px;
        height: 106px;
        box-shadow: 0px 1px 0px 0px #E8E8E8;
        line-height: 106px;
        font-size: 30px;
        margin: 0 auto;
      }
      .reception-content-li-active{
        background: url('~src/assets/order/order-ok.png') no-repeat;
        background-position: right;
        background-size: 40px 40px;
        color: #00C8B7;
      }
    }
  }
}