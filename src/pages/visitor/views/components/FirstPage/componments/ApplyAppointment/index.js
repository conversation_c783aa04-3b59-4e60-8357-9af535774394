/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-11 14:05:20
 * @LastEditTime: 2020-11-18 19:36:00
 * @LastEditors: Please set LastEditors
 * @Description: 发起预约
 */
import React, { PureComponent } from 'react'
import { List, InputItem, Switch, Toast, Picker, Button, DatePicker } from 'antd-mobile';
import Confirm from 'components/confirm'
import { createForm } from 'rc-form';
import './index.less'
import NavTopBar from 'components/NavTopBar'
import ChooseReception from '../ChooseReception';
import API from '../apis'
const Item = List.Item;
@createForm()
class ApplyAppointment extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      visible: true,
      places: [
        {
          label: '北京·朝阳区·绿地中心A座C区3层',
          value: '北京·朝阳区·绿地中心A座C区3层',
        },
        {
          label: '长沙·岳麓区·中电软件园·10栋5层',
          value: '长沙·岳麓区·中电软件园·10栋5层',
        },
        {
          label: '广州·海珠区·广州国际媒体港西港11楼',
          value: '广州·海珠区·广州国际媒体港西港11楼',
        },
      ],
      receptionFlag: false,
      AllPeopleNameLists: [],
      selectedName: '',
      urlName: window.location.origin + window.location.pathname
    }
  }

  componentDidMount() {
    this.getAllPeopleNameList()
  }

  // 获取所有人员信息
  getAllPeopleNameList = () => {
    API.getAllPeopleNameList().then(res => {
      this.setState({
        AllPeopleNameLists: res.data
      })
      this.refs.chooseRef.receiver(res.data)
    }).catch(err => {
      console.log(err)
    })
  }

  // 登记
  submitVisitorForm = (value) => {
    API.submitVisitorForm({
      email: value.email,
      name: value.name,
      place: value.place,
      purpose: value.purpose,
      receivers: value.receivers,
      start_time: value.start_time,
      telephone: value.telephone,
      temporary_account: value.temporary_account,
    }).then(res => {
      window.location.href = window.location.origin + window.location.pathname + '#/?tab=myappoint'
    }).catch(err => {
      console.log(err)
    })
  }

  onSubmit = () => {
    this.props.form.validateFields((err, values) => {
      if (err) {
        // Toast.info('必填项不能为空', 2);
        Confirm.ToastMessage('必填项不能为空')
        return
      }
      values.place = values.place[0]
      values.receivers = [values.receivers]
      values.start_time = this.ISODateString(values.start_time)
      this.submitVisitorForm(values)
    })
  }
  onReset = () => {
    this.props.form.resetFields();
  }

  onSelect = (opt) => {
    this.setState({
      visible: false,
    });
  };
  handleVisibleChange = (visible) => {
    this.setState({
      visible,
    });
  };

  // 取消选择接待人
  receptionCancal = () => {
    this.setState({
      receptionFlag: false
    })
  }

  // 确认选择接待人
  confirmOK = (name) => {
    this.setState({
      receptionFlag: false,
      selectedName: name
    })
    this.props.form.setFieldsValue({
      receivers: name
    })
  }

  // 时间格式
  ISODateString = (d) => {
    function pad(n) {
      return n < 10 ? '0' + n : n
    }
    return d.getUTCFullYear() + '-' + pad(d.getUTCMonth() + 1) + '-' + pad(d.getUTCDate()) + 'T' + pad(d.getUTCHours()) + ':' + pad(d.getUTCMinutes()) + ':' + pad(d.getUTCSeconds()) + 'Z'
  }

  render() {
    const { getFieldProps, getFieldError } = this.props.form;
    return <div className='appointment'>
      <div className='nav-bar'>
        <NavTopBar title='发起预约' JumpUrl={this.state.urlName} />
      </div>
      <form className='form'>
        <List className='appointment-content'>
          <InputItem
            {...getFieldProps('name', {
              rules: [
                {
                  min: 2,
                  message: '至少2字符'
                },
                {
                  required: true,
                  message: '请输入访客姓名'
                },
              ],
            })}
            clear
            error={getFieldError('name')}
            onErrorClick={() => {
              // Toast.info(getFieldError('name').join('、'), 2, null, false);
              Confirm.ToastMessage(getFieldError('name').join('、'))
            }}
            placeholder="请输入姓名"
            className='item-list have-star'
          >
            访客姓名：
          </InputItem>
          <InputItem
            {...getFieldProps('receivers', {
              rules: [
                { required: true, message: '请输入选择接待人' },
              ],
            })}
            error={getFieldError('receivers')}
            onErrorClick={() => {
              // Toast.info(getFieldError('receivers').join('、'), 2, null, false);
              Confirm.ToastMessage(getFieldError('receivers').join('、'))
            }}
            placeholder="请选择接待人"
            className='item-list have-star'
            onClick={() => {
              document.activeElement.blur();
              this.setState({
                receptionFlag: true
              }, () => {
                this.refs.chooseRef.handlefocus()
              })
            }}
          >
            接待人：
          </InputItem>
          <InputItem
            {...getFieldProps('telephone', {
              rules: [
                { required: true, pattern: /^1[3456789]\d{9}$/, message: '请输入正确手机号' },
              ],
            })}
            clear
            error={getFieldError('telephone')}
            onErrorClick={() => {
              // Toast.info(getFieldError('telephone').join('、'), 2, null, false);
              Confirm.ToastMessage(getFieldError('telephone').join('、'))
            }}
            placeholder="请输入手机号"
            className='item-list have-star'
          >
            手机号码：
          </InputItem>
        </List>
        <List className='appointment-content'>
          <DatePicker
            {...getFieldProps('start_time', {
              initialValue: new Date(),
              rules: [
                { required: true, message: '请选择来访日期及时间' },
              ],
            })}
          >
            <List.Item className='item-list have-star' arrow="horizontal">来访日期：</List.Item>
          </DatePicker>
          <InputItem
            {...getFieldProps('purpose', {
              rules: [
                { required: true, message: '请输入来访目的' },
              ],
            })}
            clear
            error={getFieldError('purpose')}
            onErrorClick={() => {
              // Toast.info(getFieldError('purpose').join('、'), 2, null, false);
              Confirm.ToastMessage(getFieldError('purpose').join('、'))
            }}
            placeholder="请输入来访目的"
            className='item-list have-star'
          >
            来访目的：
          </InputItem>
        </List>
        <List className='appointment-content'>
          <InputItem
            {...getFieldProps('email', {
              rules: [
                { type: 'email', message: '请输入正确邮箱地址' },
              ],
            })}
            clear
            error={getFieldError('email')}
            placeholder="请输入邮箱"
            className='item-list'
          >
            邮箱：
          </InputItem>
          <Picker
            title='选择接待地点'
            data={this.state.places}
            cols={1}
            {...getFieldProps('place', {
              initialValue: ['北京·朝阳区·绿地中心A座C区3层']
            })}
          >
            <List.Item className='item-list place-pick' arrow="horizontal">接待地点：</List.Item>
          </Picker>
          <Item
            className='item-list'
            extra={
              <Switch
                {...getFieldProps('temporary_account', {
                  initialValue: true,
                  valuePropName: 'checked'
                })}
              />
            }
          >
            是否需要WIFI账户：
          </Item>
        </List>
      </form>
      <div className='form-btn'>
        <Button
          size="small"
          inline
          onClick={this.onReset}
          className='btn-left'
        >
          重置
        </Button>
        <Button
          size="small"
          inline
          onClick={this.onSubmit}
          className='btn-right'
        >
          登记
        </Button>
      </div>
      <div className={this.state.receptionFlag ? 'choose-reception-active' : 'choose-reception-close'}>
        <ChooseReception
          ref='chooseRef'
          receptionCancal={this.receptionCancal}
          confirmOK={this.confirmOK}
        />
      </div>
    </div>
  }
}

export default ApplyAppointment
