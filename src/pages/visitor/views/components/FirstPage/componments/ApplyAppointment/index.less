.appointment{
  // background: #EEF3F3;
  // position: relative;
  display: flex;
  flex-direction: column;
  height: 100vh;
  // overflow-y: scroll;
  .nav-bar{
    flex: 0 0 auto;
  }
  .form{
    flex: 1 0 auto;
    .appointment-content{
      margin-top: 20px;
      // border-top: 20px solid #EEF3F3;
      box-shadow: 0 -20px 0 0 #EEF3F3;
      .item-list{
        height: 118px !important;
        font-size: 34px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #000000;
        // background: url('~src/assets/visitor/star.png') no-repeat;
        // background-size: 12px 12px;
        // background-position: 23px center;
        padding: 0 !important;
        .am-list-line{
          padding: 0 40px !important;
        }
      }
      .have-star{
        background: url('~src/assets/visitor/star.png') no-repeat;
        background-size: 12px 12px;
        background-position: 23px center;
      }
    }
  }
  .form-btn{
    flex: 0 0 auto;
    width: 750px;
    height: 120px;
    background: #FFFFFF;
    box-shadow: 0px -4px 12px 0px rgba(0, 0, 0, 0.06);
    display: flex;
    justify-content: space-around;
    align-items: center;
    // font-size: 30px;
    // font-family: PingFangSC-Medium, PingFang SC;
    // font-weight: 500;
    .btn-left{
      width: 306px;
      height: 88px;
      background: #F1F1F1;
      border-radius: 10px;
      color: #333333;
      line-height: 88px;
      font-size: 30px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
    }
    .btn-right{
      width: 306px;
      height: 88px;
      background: #00C8B7;
      border-radius: 10px;
      color: #FFFFFF;
      line-height: 88px;
      font-size: 30px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
    }
  }
}
.am-list-item .am-input-control input, .am-list-extra {
  font-size: 30px !important;
}
.am-list-item .am-list-line .am-list-extra{
  width: 300px;
}
.am-list-item .am-input-control {
  padding-left: 50px;
}
.place-pick .am-list-extra{
  color: red;
  width: 450px !important;
}
.place-pick .am-list-content{
  font-size: 34px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
}
.am-list-item .am-list-line .am-list-extra {
  flex: none;
}

.choose-reception-close{
  display: none;
  position: absolute;
  bottom: -100vh;
  animation: 2s receptionActiondown 1;
  -webkit-animation: 2s receptionActiondown 1;
}
.choose-reception-active{
  display: block;
  position: fixed;
  // top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  height: 100%;
  width: 100%;
  z-index: 2;
  animation: receptionAction .2s ease-in-out 1;
  -webkit-animation: receptionAction .2s ease-in-out 1;
  // animation-name: got;
  // animation-duration: 2s;
  // animation-timing-function: ease-in-out;
  // // animation-delay: 1s;
  // animation-fill-mode:reverse;
  // animation-direction: normal;
  // animation-iteration-count: 1;
}
@keyframes receptionAction {
  from { bottom: -100vh; }
  to { bottom: 0px; }
}
@-webkit-keyframes receptionAction {
  from { bottom: -100vh; }
  to { bottom: 0px; }
}
@keyframes receptionActiondown {
  from { bottom: 0; }
  to { bottom: -100vh; }
}
@-webkit-keyframes receptionActiondown {
  from { bottom: 0; }
  to { bottom: -100vh; }
}