/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-19 14:00:46
 * @LastEditTime: 2020-11-19 17:18:05
*/

import { getRequestsByRoot } from 'axios-service'
import srcConfig from 'src/config/apiRoots'

const { get, post, postXForm } = getRequestsByRoot({ root: srcConfig.familyRoot })
class Apis {
  /**
   * 接口：接待人接口
   */
  getAllPeopleNameList = get('api/v2/personal/userList', {}, { autoLoading: false })

  /**
   * 接口：提交访客登记信息
   */
  submitVisitorForm = post('api/v2/visitor/visitorAdd', {}, { autoLoading: false })
}

export default new Apis()
