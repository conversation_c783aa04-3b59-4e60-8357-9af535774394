/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-05 16:27:28
 * @LastEditTime: 2020-11-05 19:36:00
 * @LastEditors: Please set LastEditors
 * @Description: 访客预约-首页
 */
import React, { PureComponent } from 'react'
import './index.less'
import NavTopBar from 'components/NavTopBar'

class FirstPage extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      itemData: [{
        icon: require('assets/home/<USER>'),
        name: '发起预约',
      }],
      urlName: window.location.origin + '/home/<USER>'
    };
  }

  /**
   * 点击访客预约-首页中的li
   * @param { 下标 } i
   */
  applyAppointment = (i) => {
    if (i === 0) {
      this.props.history.push('/visitor/registration')
    }
  }

  render() {
    const {
      itemData
    } = this.state
    return (
      <div className='home'>
        <NavTopBar title={'访客预约'} JumpUrl={this.state.urlName} />
        <div className='home-banner'>
        </div>
        <ul className='ul-wrapper'>
          {
            itemData.map((item, i) => {
              return <li key={i} className='item-li' onClick={this.applyAppointment.bind(this, i)}>
                <img alt='' src={item.icon} className='item-icon' />
                <span>{item.name}</span>
              </li>
            })
          }
        </ul>
      </div>
    )
  }
}

export default FirstPage
