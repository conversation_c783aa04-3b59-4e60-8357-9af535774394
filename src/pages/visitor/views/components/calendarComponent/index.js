import React, { Component } from 'react'
import Calendar from 'calendar-lite'
import './index.less'

class CalendarComponent extends Component {
  constructor(props) {
    super(props)
    this.state = {
      currentDateValue: new Date().format('yyyy-MM-dd')
    }
  }

  componentDidMount() {
    this.renderCalendar()
  }

  onDayClick = date => {
    console.log(date)
    this.handleTimeChange(date)
  }

  renderCalendar = () => {
    return new Calendar({
      el: this.refs.slideRuler,
      currentDate: this.state.currentDateValue,
      format: 'yyyy-MM-dd',
      onDayClick: this.onDayClick
    });
  }

  backToday = () => {
    this.setState({
      currentDateValue: new Date().format('yyyy-MM-dd')
    }, () => {
      this.refs.slideRuler.innerHTML = ''
      // console.log(this.refs.slideRuler.innerHTML = '', this.state.currentDateValue)
      this.renderCalendar()
      this.handleTimeChange(this.state.currentDateValue, true)
    })
  }

  handleTimeChange = (time, value) => {
    this.props.handleTimeChange(time, value)
  }

  render () {
    return <div className='calendar-wrapper'>
      <div ref='slideRuler' />
      <div className='calendar-bottom'>
        <span onClick={this.backToday}>回到今天</span>
      </div>
    </div>
  }
}
export default CalendarComponent
