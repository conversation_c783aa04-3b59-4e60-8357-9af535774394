import React, { Suspense, lazy } from 'react'
import { Route, Switch, HashRouter, Browser<PERSON><PERSON>er, Link } from 'react-router-dom'
import LoadingView from 'components/_loading'

const Router = BrowserRouter

// https://reactjs.org/docs/code-splitting.html#reactlazy

const Visitor = lazy(() => import('../views'))
const ApplyAppointment = lazy(() => import('../views/components/FirstPage/componments/ApplyAppointment'))
// const MyAppointment = lazy(() => import('../views/components/MyAppointment'))

const linkStyle = {
  textDecoration: 'underline',
  margin: 10
}

const getRouter = _ => (
  <HashRouter>
    <Suspense fallback={<LoadingView />}>
      {/* 异步引入 */}
      <Route exact path="/" render={props => <Visitor {...props}/>} />
      <Route exact path="/visitor/registration" render={props => <ApplyAppointment {...props}/>} />
    </Suspense>
  </HashRouter>
)

export default getRouter
