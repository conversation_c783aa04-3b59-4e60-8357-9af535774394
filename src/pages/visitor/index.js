/*
 * @Author: your name
 * @Date: 2020-10-12 19:47:42
 * @LastEditTime: 2020-10-15 16:47:47
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /my-app/Users/<USER>/code/family-h5/src/pages/ordering/index.js
 */
import entry, { loadingUntilFirstContentPaint } from 'entry'
import React from 'react'
import App from './app'
import store from './store'
// import apiShare from './apis/share'
// import IKBridgex from 'ik-bridgex'
// import bridgex from 'bridgex'
// import $log from 'utils/log'
// import { actionDemo } from 'bridgex/actions'
// import { Toast } from '../../entry/unprerender-ui';

// import './style.less'

// console.log('React Version: ', React.version)

// console.log('bridgex: ', IKBridgex, bridgex)

// const createLog = ns => (...args) => console.log(ns, ...args)
// const createError = ns => (...args) => console.error(ns, ...args)

/* eslint-disable no-console */
// apiShare.getWxShareConfig({ myKey1: 'aaa', myKey2: 'bbb' }).then(createLog('getWxShareConfig'), createError('getWxShareConfig'))

// apiShare.postWxShareConfig({ myKey1: 'aaa', myKey2: 'bbbc' }).then(createLog('postWxShareConfig'), createError('postWxShareConfig'))

// // with mock
// apiShare.getWxShareConfigMock().then(createLog('getWxShareConfigMock'), createError('getWxShareConfigMock'))

// apiShare.getWxShareConfigMockDataSync().then(createLog('getWxShareConfigMockDataSync'), createError('getWxShareConfigMockDataSync'))

// apiShare.getWxShareConfigMockByCompose().then(createLog('getWxShareConfigMockByCompose'), createError('getWxShareConfigMockByCompose'))
/* eslint-enable no-console */

// 页面使用loading用的
loadingUntilFirstContentPaint()

// test unPrerender toast
// Toast.show('test unPrerender toast')

// $log.setBaseOptions({
//   inkewid: 'ik_h5_react_demo',
//   inkewname: 'inke_bpc_template_demo'
// });

entry(App, {
  store
})

// if (module.hot) {
//   module.hot.accept()
// }
