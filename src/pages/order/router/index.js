import React, { Suspense, lazy } from 'react'
import { Route, Switch, HashR<PERSON>er, Browser<PERSON><PERSON><PERSON>, Link } from 'react-router-dom'
import LoadingView from 'components/_loading'

const Router = BrowserRouter

// https://reactjs.org/docs/code-splitting.html#reactlazy

const Order = lazy(() => import('../views'))
const OrderPonit = lazy(() => import('../views/components/OrderList/components/OrderPoint'))

const linkStyle = {
  textDecoration: 'underline',
  margin: 10
}

const getRouter = _ => (
  <HashRouter>
    <Suspense fallback={<LoadingView />}>
      {/* 异步引入 */}
      <Route exact path="/" render={props => <Order {...props}/>} />
      <Route exact path="/orderPoint/:id" render={props => <OrderPonit {...props}/>} />
    </Suspense>
  </HashRouter>
)

export default getRouter
