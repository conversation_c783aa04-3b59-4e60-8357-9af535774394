.order{
  position: fixed;
  height: 100%;
  width: 100%;
  top: 0;
  background-color: #ffffff;
  .order-banner {
    width: 750px;
    height: 200px;
    background: url('~src/assets/order/order-banner.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .am-tab-bar-bar{
    position: fixed;
    width: 750px;
    height: 108px;
    background: #FFFFFF;
    box-shadow: 0px -4px 12px 0px rgba(0, 0, 0, 0.06);
  }
  .am-tab-bar-item{
    height: calc(100% - 108px);
    overflow: auto;
  }
  .am-tab-bar-tab-title{
    height: 30px !important;
    font-size: 22px !important;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #000000;
    line-height: 30px !important;
  }
  .orderList{
    width: 56px;
    height: 56px;
    background: url('~src/assets/order/order-list.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .orderList-active{
    width: 56px;
    height: 56px;
    background: url('~src/assets/order/order-list-active.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .rollout{
    width: 56px;
    height: 56px;
    background: url('~src/assets/order/order-rollout.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .rollout-active{
    width: 56px;
    height: 56px;
    background: url('~src/assets/order/order-rollout-active.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .myorder{
    width: 56px;
    height: 56px;
    background: url('~src/assets/order/order-myorder.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .myorder-active{
    width: 56px;
    height: 56px;
    background: url('~src/assets/order/order-myorder-active.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
}