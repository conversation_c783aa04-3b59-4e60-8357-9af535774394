/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-03 11:27:28
 * @LastEditTime: 2020-11-03 15:28:47
 * @LastEditors: Please set LastEditors
 * @Description: 转让区
 */
import React, { PureComponent } from 'react'
import NavTopBar from 'components/NavTopBar'
import { Toast } from 'antd-mobile'
import Confirm from 'components/confirm'
import Empty from 'components/Empty'
import API from './apis'
import './index.less'

class Rollout extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      describe: '转让区空空如也～',
      resourceData: []
    }
  }

  componentDidMount() {
    this.getRollOutList()
  }

  // 接收父组件传递信息
  receiveInfs = (flag) => {
    if (flag) {
      this.getRollOutList()
    }
  }

  /**
   * 获取转餐的列表
   */
  getRollOutList = () => {
    API.getRollOutList().then(res => {
      for (let i = 0; i < res.data.length; i++) {
        for (let j = 0; j < res.data[i].order_content.length; j++) {
          for (let k = 0; k < res.data[i].order_content[j].dish_list.length; k++) {
            if (res.data[i].order_content[j].dish_list[k].dishName.indexOf('(') !== -1) {
              let disName = res.data[i].order_content[j].dish_list[k].dishName.split('(')[0]
              let describeStr = res.data[i].order_content[j].dish_list[k].dishName.split('(')[1].split(')')[0]
              res.data[i].order_content[j].dish_list[k].describeStr = describeStr
              res.data[i].order_content[j].dish_list[k].dishName = disName
            }
          }
        }
      }
      this.setState({
        resourceData: res.data || []
      })
    }).catch(err => {
      console.log(err)
    })
  }

  /**
   * 抢餐
   */
  rollOut = (orderId) => {
    API.sureRollOrderMeal({
      order_id: orderId
    }).then(res => {
      // Toast.success('抢餐成功')
      Confirm.ToastMessage('抢餐成功')
    }).catch(err => {
      // Toast.fail(err.msg || err.response.data.msg)
      Confirm.ToastMessage(err.msg || err.response.data.msg)
    })
  }

  render() {
    const {
      describe,
      resourceData
    } = this.state
    return (
      <div className='rollout-content'>
        {/* <NavTopBar title='转让区' {...this.props} BackHomePage={true} /> */}
        {
          resourceData.length ? <div className='rollout-all'>
            <ul className='rollout-list-ul'>
              {
                resourceData.map((item, index) => {
                  return item.order_content.map((list, i) => {
                    return list.dish_list.map((obj, j) => {
                      return (
                        <li className='rollout-list-li' key={item.order_id}>
                          <div className='content-li-left'>
                            <p className='content-li-left-title'>{obj.dishName}</p>
                            <p className='content-li-left-content'>
                              <label>{obj.describeStr}</label>
                              <label>{list.restaurant_name}</label>
                            </p>
                          </div>
                          <span className='content-li-right' onClick={this.rollOut.bind(this, item.order_id)}>抢</span>
                        </li>
                      )
                    })
                  })
                })
              }
            </ul>
          </div> : <Empty className='empty-val' describe={describe} />
        }
      </div>
    )
  }
}

export default Rollout
