/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-10 16:00:40
 * @LastEditTime: 2020-11-10 17:35:00
*/

import { getRequestsByRoot } from 'axios-service'
import srcConfig from 'src/config/apiRoots'

const { get, post, postXForm, restFulDelete } = getRequestsByRoot({ root: srcConfig.familyRoot })
class Apis {
  /**
   * 获取抢餐列表
   */
  getRollOutList = get('api/v2/meal/order/transfer/list', {}, { autoLoading: false })

  /**
   * 确认抢到的餐品
   */
  sureRollOrderMeal = post('api/v2/meal/order/grab/confirm', {}, { autoLoading: false })
}

export default new Apis()
