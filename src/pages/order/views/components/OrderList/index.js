/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-03 11:27:28
 * @LastEditTime: 2020-11-20 15:49:47
 * @LastEditors: Please set LastEditors
 * @Description: 点餐中心
 */
import React, { PureComponent } from 'react'
import { Rate } from 'antd'
// import { Toast } from 'antd-mobile'
import Confirm from 'components/confirm'
import NavTopBar from 'components/NavTopBar'
import API from './apis'
import setTitle from 'components/setBarTitle'
import './index.less'

class OrderList extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      orderListData: [],
      urlName: window.location.origin + '/home/<USER>'
    }
  }

  componentDidMount() {
    setTitle('订餐中心')
    this.getOrderList()
  }

  // 接收父组件传递信息
  receiveInfs = (flag) => {
    if (flag) {
      this.getOrderList()
    }
  }

  // 获取餐厅列表
  getOrderList = () => {
    API.getOrderList().then(res => {
      // res.data.restaurant_list[0].status = !res.data.restaurant_list[0].status // 测试使用,最后记得要删掉
      this.setState({
        orderListData: res.data.restaurant_list
      })
    }).catch(err => {
      console.log(err)
    })
  }

  handleFun = (item) => {
    this.props.history.push('/orderPoint/' + item.restaurant_id)
  }

  render() {
    const { orderListData } = this.state
    return (
      <div className='order-list'>
        {/* <NavTopBar title='点餐中心' JumpUrl={this.state.urlName} /> */}
        <div className='order-list-banner'></div>
        <div className='order-list-all'>
          <ul className='order-list-ul'>
            {
              orderListData && orderListData.map((item, index) => {
                if (item.status) {
                  return <li className='order-list-li order-list-li-close'
                    key={item.restaurant_id}
                    onClick={
                      // () => Toast.fail('该订餐已满，餐厅已关闭', 1)
                      () => Confirm.ToastMessage('该订餐已满，餐厅已关闭')
                    }>
                    <div className='content'>
                      <span>{item.restaurant_name}</span>
                      <span>
                        <Rate
                          count={3}
                          defaultValue={2}
                          className='order-stars'
                        />
                      </span>
                    </div>
                  </li>
                } else {
                  return <li className='order-list-li order-list-li-active' key={item.restaurant_id} onClick={this.handleFun.bind(this, item)}>
                    <div className='content'>
                      <span>{item.restaurant_name}</span>
                      <span>
                        <Rate
                          count={3}
                          defaultValue={2}
                          className='order-stars'
                        />
                      </span>
                    </div>
                  </li>
                }
              })
            }
          </ul>
        </div>
      </div>
    )
  }
}

export default OrderList
