.order-list{
  background-color: #ffffff;
  .order-list-banner {
    width: 750px;
    height: 200px;
    background: url('~src/assets/order/order-banner.png') no-repeat;
    background-position: center;
    background-size: contain;
  }
  .ul-wrapper {
    font-size: 36px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #000000;
    margin-top: 40px;
    .item-icon {
      width: 98px;
      height: 98px;
      margin: 0 36px 0 10px;
    }
    .item-li {
      width: 690px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      height: 160px;
      box-shadow: 0px 1px 0px 0px #E8E8E8;
    }
    .item-li:active {
      background: #00C8B7;
      color: #fff;
    }
  }
  .order-list-all{
    .order-list-ul{
      font-size: 34px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #000000;
      margin-bottom: 0 !important;
      .order-list-li{
        width: 750px;
        .content{
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          width: 690px;
          height: 150px;
          padding: 0 10px;
          // box-shadow: 0px 1px 0px 0px #E8E8E8;
          margin: 0 auto;
        }
      }
      .order-list-li-close{
        background:  rgba(217,217,217,.85);
        border-bottom: 1px solid rgba(130,128,128,.1);
        span:nth-child(1) {
          color: rgba(0,0,0,.25);
        }
        &:last-child{
          border: none;
        }
      }
      .order-list-li-active{
        .content{
          box-shadow: 0px 1px 0px 0px #E8E8E8;
        }
        &:active{
          background: #00C8B7;
          color: #fff;
        }
      }
      .order-stars{
        font-size: 30px;
      }
    }
  }
}