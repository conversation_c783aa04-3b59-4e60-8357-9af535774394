.point{
  .point-ul{
    // margin-bottom: 0;
    margin-bottom: 108px;
    // height: calc(100% - 108px - 88px);
    overflow: auto;
    .point-li{
      width: 750px;
      height: 150px;
      .point-content{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 690px;
        height: 150px;
        margin: 0 auto;
        // box-shadow: 0px 1px 0px 0px #E8E8E8;
        .point-li-left{
          display: flex;
          flex-direction: column;
          font-family: PingFangSC-Regular, PingFang SC;
          span:nth-child(1) {
            font-size: 34px;
            font-weight: 500;
            line-height: 56px;
          }
          span:nth-child(2) {
            width: 500px;
            font-size: 24px;
            font-weight: 400;
            line-height: 33px;
            overflow : hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
        .logo-ok{
          width: 46px;
          height: 46px;
          background: url('~src/assets/order/order-ok.png') no-repeat;
          background-position: center;
          background-size: contain;
        }
      }
      &:active{
        background: #F9F9F9;
      }
    }
    .point-li-close{
      background:rgba(217,217,217,.85);
      box-shadow: 0px 1px 0px 0px rgba(130,128,128,.65);
      &:active{
        background: rgba(217,217,217,.85);
      }
      .point-li-left{
        span{
          color: rgba(0, 0, 0, 0.25);
        }
      }
    }
    .point-li-active{
      .point-content{
        box-shadow: 0px 1px 0px 0px #E8E8E8;
      }
      .point-li-left{
        span:nth-child(1) {
          color: #000000;
        }
        span:nth-child(2) {
          color: #666666;
        }
      }
    }
  }
  .select-active{
    margin-bottom: 0 !important;
  }
  .point-bottom{
    position: fixed;
    width: 750px;
    height: 108px;
    background: #00C8B7;
    box-shadow: 0px -1px 8px 0px rgba(0, 0, 0, 0.06);
    bottom: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    .point-bottom-left{
      .point-bottom-left-img{
        width: 50px;
        height: 38px;
        margin-left: 30px;
      }
      span{
        margin: -4px 0 0 15px;
        font-size: 28px;
        color: #FFFFFF;
        line-height: 40px;
      }
    }
    .point-bottom-right{
      width: 126px;
      height: 60px;
      background: #FFFFFF;
      border-radius: 30px;
      margin-right: 30px;
      text-align: center;
      line-height: 60px;
      font-size: 30px;
      color: #00C8B7;
    }
  }
}