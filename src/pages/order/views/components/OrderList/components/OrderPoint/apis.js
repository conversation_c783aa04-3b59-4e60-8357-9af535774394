/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-10 11:00:46
 * @LastEditTime: 2020-11-10 11:18:05
*/

import { getRequestsByRoot } from 'axios-service'
import srcConfig from 'src/config/apiRoots'

const { get, post, postXForm } = getRequestsByRoot({ root: srcConfig.familyRoot })
class Apis {
  /**
   * 获取订餐地址
   */
  getDingAddress = get('api/v2/meal/address/list', {}, { autoLoading: false })

  /**
   * 判断是否已经下过订单
   */
  ifHaveOrder = get('api/v2/meal/order/have', {}, { autoLoading: false })

  /**
   * 下单
   */
  placeAnOrder = post('api/v2/meal/order')
}

export default new Apis()
