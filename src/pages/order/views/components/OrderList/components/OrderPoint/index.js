/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-03 14:27:28
 * @LastEditTime: 2020-11-03 20:28:47
 * @LastEditors: Please set LastEditors
 * @Description: 订餐
 */
import React, { PureComponent } from 'react'
import { Tabs, Toast } from 'antd-mobile'
import Confirm from 'components/confirm'
// import NavTopBar from 'components/NavTopBar'
import './index.less'
import S from './apis'
import API from '../../apis'
import setTitle from 'components/setBarTitle'

class OrderPoint extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      OrderTypeData: {},
      tabsList: [],
      selectIndex: undefined,
      selectOrderItem: '',
      // dish_id: undefined,
      flag: false,
      addressId: '', // 订餐ID
      haveOrder: false,
      ifOvertime: false,
      urlName: window.location.origin + '/order/index.html'
    };
  }

  componentDidMount() {
    this.getDingAddress()
    this.getOrderList()
    this.ifHaveOrder()
  }

  // 获取餐厅列表数据
  getOrderList = () => {
    const orderId = this.props.match.params.id // 获取当前路由参数ID
    API.getOrderList().then(res => {
      res.data.restaurant_list.forEach(item => {
        if (item.restaurant_id === orderId) {
          this.setState({
            OrderTypeData: item
          })
          setTitle(item.restaurant_name)
          const arr = []
          item.section_list.forEach(ele => {
            ele.dish_list.forEach((v, j) => {
              if (v.name.indexOf('(') !== -1) {
                let name = v.name.split('(')[0]
                let describeStr = v.name.split('(')[1].split(')')[0]
                v.name = name
                v.describeStr = describeStr
              }
            })
            arr.push({
              title: ele.section_name,
              dish_list: ele.dish_list
            })
          })
          this.setState({
            tabsList: arr
          })
        }
      })
    }).catch(err => {
      console.log(err)
    })
  }

  /**
   * 获取订餐信息
   */
  getDingAddress = () => {
    S.getDingAddress().then(res => {
      this.setState({
        addressId: res.data[0].id
      })
    }).catch(err => {
      console.log(err)
    })
  }

  /**
   * 查看是否已经下过单
   */
  ifHaveOrder = () => {
    let y = new Date().getFullYear()
    let m = Number(new Date().getMonth()) + 1
    let d = new Date().getDate()
    let timeStr = y + '/' + m + '/' + d + ' 18:00'
    let currentStr = new Date(new Date().format('yyyy/MM/dd hh:ss')).getTime()
    S.ifHaveOrder().then(res => {
      if (new Date(timeStr).getTime() - currentStr > 0) {
        this.setState({
          haveOrder: res.data.exist,
          ifOvertime: false
        })
      } else {
        this.setState({
          haveOrder: res.data.exist,
          ifOvertime: true
        })
      }
    }).catch(err => {
      console.log(err)
    })
  }

  /**
   * 下单接口
   * @param { 选中菜品id } dishid
   */
  requsetPlaceAnOrder = (dishid) => {
    S.placeAnOrder({
      address_id: this.state.addressId,
      email: localStorage.getItem('email'),
      order: [
        {
          dish_id: dishid,
          count: 1
        }
      ]
    }).then(res => {
      Confirm.ToastMessage('下单成功')
      window.location.href = window.location.origin + window.location.pathname + '#/?tab=myorder'
      // Toast.success('下单成功', 1, () => {
      //   window.location.href = window.location.origin + window.location.pathname + '#/?tab=myorder'
      // })
    }).catch(err => {
      console.log(err)
    })
  }

  /**
   * 点击意向餐品
   * @param { 该餐品信息 } item
   */
  selectOrder = (item) => {
    if (this.state.selectIndex === item.dish_id) {
      this.setState({
        selectIndex: undefined,
        selectOrderItem: '',
        flag: true
      })
    } else {
      this.setState({
        selectIndex: item.dish_id,
        selectOrderItem: item.name,
        flag: true
      })
    }
  }

  /**
   * 超时订餐或者重复订餐
   * @param { 是否超时 boolean } a
   * @param { 是否已有订单(订餐) boolean} b
   */
  handleTimeoutPlaceOrder = (a, b) => {
    if (a) {
      // Toast.fail('您已订过餐，不可重复订餐哦～')
      Confirm.ToastMessage('您已订过餐，不可重复订餐哦～')
    } else if (b) {
      // Toast.fail('错过订餐时间，下次早来哦～')
      Confirm.ToastMessage('错过订餐时间，下次早来哦～')
    }
  }

  /**
   * Tab 显示的餐品种类
   * @param { 餐品信息 } e
   */
  tabsContentFun = (e) => {
    return <ul className={(this.state.ifOvertime || this.state.haveOrder) ? 'point-ul select-active' : 'point-ul'}>
      {
        e.dish_list.map((item, i) => {
          if (this.state.ifOvertime || this.state.haveOrder) {
            return <li className='point-li point-li-close' key={item.dish_id} onClick={this.handleTimeoutPlaceOrder.bind(this, this.state.haveOrder, this.state.ifOvertime)}>
              <div className='point-content'>
                <div className='point-li-left'>
                  <span>{item.name}</span>
                  <span>{item.describeStr}</span>
                </div>
                <div className={ this.state.selectIndex === item.dish_id ? 'logo-ok' : ''}></div>
              </div>
            </li>
          } else {
            return <li className='point-li point-li-active' key={item.dish_id} onClick={this.selectOrder.bind(this, item)}>
              <div className='point-content'>
                <div className='point-li-left'>
                  <span>{item.name}</span>
                  <span>{item.describeStr}</span>
                </div>
                <div className={ this.state.selectIndex === item.dish_id ? 'logo-ok' : ''}></div>
              </div>
            </li>
          }
        })
      }
    </ul>
  }

  /**
   * 点击下单
   *  @param { 菜品id } dishid
   */
  placeOrder = (dishid) => {
    if (this.state.ifOvertime || this.state.haveOrder) {
      return
    }
    this.requsetPlaceAnOrder(dishid)
  }

  render () {
    const {
      OrderTypeData,
      tabsList,
      selectIndex,
      selectOrderItem
    } = this.state
    return (
      <>
        {
          OrderTypeData && tabsList.length ? <div className='point'>
            {/* <NavTopBar title={OrderTypeData.restaurant_name} JumpUrl={this.state.urlName} /> */}
            <Tabs
              className='point-tab'
              tabs={tabsList}
              useOnPan
              renderTabBar={props => <Tabs.DefaultTabBar {...props} page={3} />}
              // onChange={(e) => {
              //   this.handleTabChange(e)
              // }}
            >
              {this.tabsContentFun}
            </Tabs>
            {
              selectIndex ? <div className='point-bottom'>
                <div className='point-bottom-left'>
                  <img alt='' src={require('assets/order/order-wan.png')} className='point-bottom-left-img' />
                  <span>{selectOrderItem}（x1）</span>
                </div>
                <span className='point-bottom-right' onClick={this.placeOrder.bind(this, selectIndex)}>下单</span>
              </div> : null
            }
          </div> : null
        }
      </>
    )
  }
}

export default OrderPoint
