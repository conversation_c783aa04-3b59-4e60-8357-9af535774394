/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-10 11:00:46
 * @LastEditTime: 2020-11-10 11:18:05
*/

import { getRequestsByRoot } from 'axios-service'
import srcConfig from 'src/config/apiRoots'

const { get, post, postXForm } = getRequestsByRoot({ root: srcConfig.familyRoot })
class Apis {
  // 获取餐厅列表
  getOrderList = get('api/v2/meal/menu/list', {}, { autoLoading: false })
}

export default new Apis()
