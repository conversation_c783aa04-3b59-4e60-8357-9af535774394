/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-03 11:27:28
 * @LastEditTime: 2020-11-03 15:28:47
 * @LastEditors: Please set LastEditors
 * @Description: 我的订单
 */
import React, { PureComponent } from 'react'
import NavTopBar from 'components/NavTopBar'
// import { Toast, Modal } from 'antd-mobile'
import Empty from 'components/Empty'
import './index.less'
import API from './apis'
import Confirm from 'components/confirm'

class MyOrder extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      describe: '您还没有订单哦',
      orderId: '',
      orderStatus: 1,
      orderStatusText: '转出',
      orderCancelBtnText: '删除',
      myOrderList: [],
    }
  }

  componentDidMount() {
    this.getMyOrder()
  }

  // 接收父组件传递信息
  receiveInfs = (flag) => {
    if (flag) {
      this.getMyOrder()
    }
  }

  /**
   * 我的订单
   */
  getMyOrder = () => {
    let startTime = new Date().format('yyyy-MM-dd')
    let endTime = new Date(new Date().getTime() + 24 * 60 * 60 * 1000).format('yyyy-MM-dd')
    API.myOrder({
      start_time: startTime,
      end_time: endTime
    }).then(res => {
      let txt = '转出'
      this.setState({
        ifShowCancel: false,
        orderCancelBtnText: '删除'
      })
      if (!res.data.length) {
        this.setState({
          myOrderList: []
        })
        return
      }
      switch (res.data[0].status) {
        case 0:
          txt = '已取消'
          break;
        case 1:
          txt = '转出'
          this.setState({
            ifShowCancel: true,
            orderCancelBtnText: '删除'
          })
          break;
        case 2:
          txt = '转出中'
          this.setState({
            ifShowCancel: true,
            orderCancelBtnText: '取消'
          })
          break;
        case 3:
          if (localStorage.getItem('email') === res.data[0].email) {
            txt = '已转出'
            this.setState({
              ifShowCancel: false,
              // orderCancelBtnText: '取消抢餐',
              judgeEmail: localStorage.getItem('email') === res.data[0].email
            })
          } else {
            txt = '确认抢餐'
            this.setState({
              ifShowCancel: true,
              orderCancelBtnText: '取消抢餐',
              judgeEmail: localStorage.getItem('email') === res.data[0].email
            })
          }
          break
        default:
          break;
      }
      let arr = res.data[0].order_content
      for (let i = 0; i < arr.length; i++) {
        for (let j = 0; j < arr[i].dish_list.length; j++) {
          if (arr[i].dish_list[j].dishName.indexOf('(') !== -1) {
            let dishName = arr[i].dish_list[j].dishName.split('(')[0]
            let describeStr = arr[i].dish_list[j].dishName.split('(')[1].split(')')[0]
            arr[i].dish_list[j].dishName = dishName
            arr[i].dish_list[j].describeStr = describeStr
          } else {
            arr[i].dish_list[j].describeStr = ''
          }
        }
      }
      this.setState({
        myOrderList: res.data,
        orderId: res.data[0].order_id,
        orderStatus: res.data[0].status,
        orderStatusText: txt
      })
    }).catch(err => {
      console.log(err)
    })
  }

  /**
   * 取消餐
   */
  cancelRollOrderMeal = () => {
    API.cancelRollOrderMeal({
      order_id: this.state.orderId
    }).then(res => {
      // Toast.success('取消成功')
      Confirm.ToastMessage('取消成功')
      this.getMyOrder()
    }).catch(err => {
      console.log(err)
    })
  }
  cancelOrder = () => {
    API.deleteDing({}, {
      order_id: this.state.orderId
    }).then(res => {
      // Toast.success('删除成功', 1)
      Confirm.ToastMessage('删除成功')
      this.setState({
        myOrderList: []
      })
    }).catch(err => {
      console.log(err)
    })
  }

  /**
   * 取消转餐
   */
  cancelRollMeal = () => {
    API.cancelRollOrder({
      order_id: this.state.orderId
    }).then(res => {
      // Toast.success('取消成功')
      Confirm.ToastMessage('取消成功')
      this.getMyOrder()
    }).catch(err => {
      console.log(err)
    })
  }

  rollCanelFun = () => {
    let that = this
    if (this.state.orderStatus === 1) {
      // Modal.alert('删除订单', '你确定要删除订单么？', [
      //   { text: '取消', onPress: () => console.log('cancel') },
      //   { text: '确定', onPress: () => that.cancelOrder() },
      // ])
      Confirm.showConfirm('删除订单', '你确定要删除订单么？', () => {
        console.log('点击取消')
      }, () => {
        console.log('点击确定')
        that.cancelOrder()
      })
    } else if (this.state.orderStatus === 2) {
      // Modal.alert('取消转餐', '你确定要取消转餐么？', [
      //   { text: '取消', onPress: () => console.log('cancel') },
      //   { text: '确定', onPress: () => that.cancelRollMeal() },
      // ])
      Confirm.showConfirm('取消转餐', '你确定要取消转餐么？', () => {
        console.log('点击取消')
      }, () => {
        // console.log('点击确定')
        that.cancelRollMeal()
      })
    } else if (this.state.orderStatus === 3) {
      // Modal.alert('取消抢到的餐', '你确定要取消转餐么？', [
      //   { text: '取消', onPress: () => console.log('cancel') },
      //   { text: '确定', onPress: () => that.cancelRollOrderMeal() },
      // ])
      Confirm.showConfirm('取消抢到的餐', '你确定要取消转餐么？', () => {
        console.log('点击取消')
      }, () => {
        // console.log('点击确定')
        that.cancelRollOrderMeal()
      })
    }
    // this.cancelOrder()
  }

  /**
   * 转出订餐
   */
  rollMeal = () => {
    API.rollMeal({
      order_id: this.state.orderId
    }).then(res => {
      // Toast.success('操作成功，正在等待别人抢餐')
      Confirm.ToastMessage('操作成功，正在等待别人抢餐')
      this.getMyOrder()
    }).catch(err => {
      console.log(err)
    })
  }

  /**
   * 转餐
   */
  rollOut = () => {
    if (this.state.orderStatus === 1) {
      this.rollMeal()
    } else if (this.state.orderStatus === 2) {
      // Toast.info('正在转出中，请稍候...')
      Confirm.ToastMessage('正在转出中，请稍候...')
    } else if (this.state.orderStatus === 3) {
      if (this.state.judgeEmail) {
        // Toast.info('餐已被抢走，等待对方确认中，请稍等...')
        Confirm.ToastMessage('餐已被抢走，等待对方确认中，请稍等...')
      } else {
        this.sureRollOrderMeal()
      }
    }
  }

  /**
   * 确认抢到的餐
   */
  sureRollOrderMeal = () => {
    API.sureRollOrderMeal({
      order_id: this.state.orderId
    }).then(res => {
      // Toast.success('确认抢单成功')
      Confirm.ToastMessage('确认抢单成功')
      this.getMyOrder()
    }).catch(err => {
      // Toast.error(err.msg)
      Confirm.ToastMessage(err.msg)
    })
  }

  render() {
    const { myOrderList, describe } = this.state
    return (
      <div className='myorder-content'>
        {/* <NavTopBar title='我的订单' {...this.props} BackHomePage={true} /> */}
        {
          myOrderList.length ? myOrderList.map((item, index) => {
            return item.order_content.map((obj, j) => {
              return obj.dish_list.map((list, k) => {
                return (
                  <div className='myorder-num' key={k}>
                    <div className='myorder-list-content'>
                      <div className='myorder-num-top'>
                        <span>{list.dishName}</span>
                        <span>{list.describeStr}</span>
                        <span>{obj.restaurant_name}</span>
                      </div>
                      {
                        localStorage.getItem('email') && localStorage.getItem('email') !== item.email
                          ? <span className='mealor-name'>{item.real_name}-转出</span>
                          : <span className='mealor-name'>{item.real_name}</span>
                      }
                    </div>
                    <div className='myorder-num-bottom'>
                      <span onClick={this.rollOut}>{this.state.orderStatusText}</span>
                      <span></span>
                      {
                        this.state.ifShowCancel ? <span onClick={this.rollCanelFun}>{this.state.orderCancelBtnText}</span> : ''
                      }
                    </div>
                  </div>
                )
              })
            })
          }) : <Empty className='empty-val' describe={describe} />
        }
      </div>
    )
  }
}

export default MyOrder
