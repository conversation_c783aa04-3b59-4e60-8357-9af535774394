/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-10 11:00:46
 * @LastEditTime: 2020-11-10 16:18:05
*/

import { getRequestsByRoot } from 'axios-service'
import srcConfig from 'src/config/apiRoots'

const { get, post, postXForm, restFulDelete } = getRequestsByRoot({ root: srcConfig.familyRoot })
class Apis {
  /**
   * 我的订单
   */
  myOrder = get('api/v2/meal/order/list', {}, { autoLoading: false })

  /**
   * 抢餐
   */
  getRobDing = post('api/v2/meal/order/grab', {}, { autoLoading: false })
  deleteDing = restFulDelete('api/v2/meal/order', {}, { autoLoading: false })

  /**
   * 取消转餐
   */
  cancelRollOrder = post('api/v2/meal/order/roll/cancel', {}, { autoLoading: false })

  /**
   * 转餐
   */
  rollMeal = post('api/v2/meal/order/roll', {}, { autoLoading: false })
}

export default new Apis()
