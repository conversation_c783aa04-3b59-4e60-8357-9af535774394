.myorder-content{
  background: #F0F5F5;
  height: 100vh;
  .myorder-num{
    width: 690px;
    height: 306px;
    background: #FFFFFF;
    border-radius: 10px;
    margin: 30px auto 0;
    display: flex;
    flex-direction: column;
    .myorder-list-content {
      display: flex;
      flex-direction: row;
      padding: 30px;
    }
    .mealor-name {
      font-weight: 500;
    }
    .myorder-num-top{
      display: flex;
      flex-direction: column;
      flex: 1;
      // padding: 30px;
      span:nth-child(1) {
        font-size: 36px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #000000;
      }
      span:nth-child(2) {
        width: 100%;
        font-size: 28px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 42px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
      span:nth-child(3) {
        width: fit-content;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        line-height: 42px;
        color: #666666;
        overflow: hidden;
        background: #F2F2F2;
      }
    }
    .myorder-num-bottom{
      height: 88px;
      box-shadow: 0px -1px 0px 0px #E8E8E8;
      display: flex;
      flex-direction: row;
      align-items: center;
      text-align: center;
      font-size: 30px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      span:nth-child(1) {
        flex: 1;
        color: #00C8B7;
      }
      span:nth-child(2) {
        display: inline-block;
        width: 1px;
        height: 36px;
        background: #DBDBDB;
      }
      span:nth-child(3) {
        flex: 1;
      }
    }
  }
}
.empty-val {
  width: max-content;
  position: relative;
  left: 50%;
  top: 50%;
  margin-top: -108px;
  transform: translate(-50%, -50%);
}
