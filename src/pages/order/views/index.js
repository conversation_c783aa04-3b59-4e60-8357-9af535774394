/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-03 14:27:28
 * @LastEditTime: 2020-11-10 20:28:47
 * @LastEditors: Please set LastEditors
 * @Description: 订餐
 */
import React, { PureComponent } from 'react'
import { TabBar } from 'antd-mobile'
import './index.less'

import OrderList from './components/OrderList'
import Rollout from './components/Rollout'
import MyOrder from './components/MyOrder'
import setTitle from 'components/setBarTitle'

class Order extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      selectedTab: 'orderList',
      bottomNavBar: [
        {
          title: '餐厅列表',
          key: 'orderList',
          url: '#/'
        },
        {
          title: '转让区',
          key: 'rollout',
          url: '#/?tab=rollout'
        },
        {
          title: '我的订单',
          key: 'myorder',
          url: '#/?tab=myorder'
        }
      ],
      valueId: 0,

    };
  }

  componentDidMount () {
    let urlHashValue = window.location.hash
    switch (urlHashValue) {
      case '#/':
        this.setState({
          valueId: 0,
          selectedTab: 'orderList'
        })
        window.location.href = window.location.origin + window.location.pathname + urlHashValue
        if (this.refs.OrderList) this.refs.OrderList.receiveInfs(true)
        break;
      case '#/?tab=rollout':
        this.setState({
          valueId: 1,
          selectedTab: 'rollout'
        })
        window.location.href = window.location.origin + window.location.pathname + urlHashValue
        if (this.refs.Rollout) this.refs.Rollout.receiveInfs(true)
        break;
      case '#/?tab=myorder':
        this.setState({
          valueId: 2,
          selectedTab: 'myorder'
        })
        window.location.href = window.location.origin + window.location.pathname + urlHashValue
        if (this.refs.MyOrder) this.refs.MyOrder.receiveInfs(true)
        break;
      default:
        this.setState({
          valueId: 0,
          selectedTab: 'orderList'
        })
        window.location.href = window.location.origin + window.location.pathname + urlHashValue
        if (this.refs.OrderList) this.refs.OrderList.receiveInfs(true)
        break;
    }
  }

  // renderContent(pageText, index) {
  //   switch (index) {
  //     case 0:
  //       return <OrderList ref='OrderList' title={pageText.title} {...this.props} />
  //     case 1:
  //       return <Rollout ref='Rollout' title={pageText.title} />
  //     case 2:
  //       return <MyOrder ref='MyOrder' title={pageText.title} />
  //     default:
  //       break
  //   }
  // }

  /**
   * 点击 tab 回调函数
   * @param { tab的下标 } e
   * @param { tab参数信息 } item
   */
  handleNavBarBottomFun = (e, item) => {
    switch (e) {
      case 0:
        this.setState({
          valueId: 0,
          selectedTab: 'orderList'
        })
        setTitle('餐厅列表')
        window.location.href = window.location.origin + window.location.pathname + item.url
        if (this.refs.OrderList) this.refs.OrderList.receiveInfs(true)
        break
      case 1:
        this.setState({
          valueId: 1,
          selectedTab: 'rollout'
        })
        setTitle('转让区')
        window.location.href = window.location.origin + window.location.pathname + item.url
        if (this.refs.Rollout) this.refs.Rollout.receiveInfs(true)
        break
      case 2:
        this.setState({
          valueId: 2,
          selectedTab: 'myorder'
        })
        setTitle('我的订单')
        window.location.href = window.location.origin + window.location.pathname + item.url
        if (this.refs.MyOrder) this.refs.MyOrder.receiveInfs(true)
        break
      default:
        break
    }
  }

  // 回到首页
  backNowFirstPage = () => {
    this.setState({
      valueId: 0,
      selectedTab: 'orderList'
    })
    window.location.href = window.location.origin + window.location.pathname + '#/'
    if (this.refs.OrderList) this.refs.OrderList.receiveInfs(true)
  }

  render () {
    const { bottomNavBar, selectedTab } = this.state
    return (
      <div className='order'>
        <TabBar
          unselectedTintColor="#000000"
          tintColor="#000000"
          barTintColor="#FFFFFF"
          tabBarPosition='bottom'
          className='order-tabBar'
          prerenderingSiblingsNumber={0}
        >
          {
            bottomNavBar.map((item, index) => {
              return (
                <TabBar.Item
                  title={item.title}
                  key={item.key}
                  icon={<div className={item.key} />}
                  selectedIcon={<div className={`${item.key}-active`} />}
                  selected={selectedTab === item.key}
                  onPress={() => {
                    this.setState({
                      selectedTab: item.key
                    })
                    this.handleNavBarBottomFun(index, item)
                  }}
                >
                  {/* {this.renderContent(item, index)} */}
                  {
                    this.state.valueId === 0 ? <OrderList ref='OrderList' title={item.title} {...this.props} backNowFirstPage={this.backNowFirstPage} /> : ''
                  }
                  {
                    this.state.valueId === 1 ? <Rollout ref='Rollout' title={item.title} {...this.props} backNowFirstPage={this.backNowFirstPage} /> : ''
                  }
                  {
                    this.state.valueId === 2 ? <MyOrder ref='MyOrder' title={item.title} {...this.props} backNowFirstPage={this.backNowFirstPage} /> : ''
                  }
                </TabBar.Item>
              )
            })
          }
        </TabBar>
      </div>
    )
  }
}

export default Order
