const scriptsMap = require('../../scriptsMap')
const isEnvDevelopment = process.env.NODE_ENV === 'development'
const utils = require('../../utils')

exports.index = {
  index: utils.getEntryViewConfig({
    title: '映客直播',
    description: '最牛逼的直播',
    // baseSize: 100,
    // isPx2rem: false
  })
}

/**
 * 参数说明
 * @param title
 * @param keywords
 * @param description
 * @param injectScript script标签, 注意需要带http路径
 * @param customTemplate 自定义模板, 可以在公共public.html内注入相关自一定html
 * @param customTemplateHeader 自定义模板, 可以在公共public.html head内注入相关自一定html
 * @param isPx2rem 表示是否使用px2rem插件, 将单位进行转换, 此开关默认开始, 如果开闭适合pc端应用, 注意细节: 如果关闭此开关的话, 不能引入ant-mobile组件, 应该引用antd组件
 * @param baseSize 宽度配置，默认750，可以根据业务webview的宽度动态地配置
 * @return {{keywords: string, description: string, title: string}}
 */
exports.demo = {
  // 指向pages文件夹对应webpack entry的路径, 如果是二级目录, 为: 'demo/pagea' 指向文件为: src/pages/demo/pagea/index.js
  demo: {
    title: 'demo',
    description: '最牛逼的demo',
    prerenderOptions: {
      routes: ['/']
    },
    // customTemplate: `
    //   <script>
    //     var __A__ = 1
    //   </script>
    // `
    // customTemplateHeader: '<meta name="flexible" content="user-scalable=yes"/>',
    customTemplateHeader: ' <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=yes" />',
    // customTemplate: utils.readPagesFile('demo/customTemplate.html'),
    injectScript: [scriptsMap.WX_SDK, isEnvDevelopment && '//:a.js'].filter(Boolean),
    baseSize: 750,
    isPx2rem: true,
    // useArms 内支持传函数，必须使用 es5 编写，且不能引用外部包
    // 更多监控写法: https://wiki.inkept.cn/pages/viewpage.action?pageId=86318060
    // useArms: true,
    // 如需对重要接口进行监控，则按照如下方式使用
    // useArms: {
    //   disableHook: false, // 开启接口监控
    //   ignore: {
    //     ignoreApis: [
    //       /*
    //        * 仅上报重要接口示例
    //        * 返回 true 则忽略，返回 false 则上报
    //        */
    //       function (str) {
    //         return !(str && [
    //           '/api/some-important-api',
    //           '/api/some-important-api2',
    //         ].some(function (api) {
    //           return str.indexOf(api) > -1
    //         }))
    //       }
    //     ]
    //   }
    // }
  }
}
exports.home = {
  home: {
    title: '',
    description: 'Family首页',
    prerenderOptions: {
      routes: ['/home']
    },
    customTemplateHeader: ' <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=yes" />',
    baseSize: 750,
    isPx2rem: true,
  }
}

exports.order = {
  order: {
    title: '',
    description: '点餐中心',
    prerenderOptions: {
      routes: ['/order']
    },
    customTemplateHeader: ' <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=yes" />',
    baseSize: 750,
    isPx2rem: true,
  }
}

exports.visitor = {
  visitor: {
    title: '',
    description: '访客预约',
    prerenderOptions: {
      routes: ['/visitor']
    },
    customTemplateHeader: ' <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=yes" />',
    baseSize: 750,
    isPx2rem: true,
  }
}

exports.flow = {
  flow: {
    title: '',
    description: '流程审批',
    prerenderOptions: {
      routes: ['/']
    },
    customTemplateHeader: ' <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=yes" />',
    baseSize: 750,
    isPx2rem: true,
  }
}

// 固定资产管理
exports.asset = {
  asset: {
    title: '',
    description: '资产管理',
    prerenderOptions: {
      routes: ['/asset']
    },
    customTemplateHeader: ' <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=yes" />',
    baseSize: 750,
    isPx2rem: true,
  }
}

exports.skip = {
  skip: {
    title: '',
    description: '跳转',
    prerenderOptions: {
      routes: ['/skip']
    },
    customTemplateHeader: ' <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=yes" />',
    baseSize: 750,
    isPx2rem: true,
  }
}

exports.example = {
  example: utils.getEntryViewConfig({
    title: 'example'
  })
}
