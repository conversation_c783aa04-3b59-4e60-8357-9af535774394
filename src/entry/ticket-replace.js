/*
 * @Author: your name
 * @Date: 2019-11-29 15:06:30
 * @LastEditTime: 2019-12-19 15:27:26
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /family.inkept.cn/src/entry/ticket-replace.js
 */

export default _ => {
  const reg = /\?ticket=[A-Za-z0-9]+/;
  const href = window.location.href;
  const hrefNoTicket = href.replace(reg, '');
  if (reg.test(href)) {
    if (window.history && window.history.replaceState) {
      window.history.replaceState(null, document.title, hrefNoTicket);
    } else {
      window.location.href = hrefNoTicket;
    }
  }
};
