/**
 * 判断用户是否登录，以及ticket是否生效
 */
import axio from 'axios'
import { getRequestsByRoot, serviceHocs, version as axiosServiceVersion } from 'axios-service'
// import { inkeCommonResponseKeys } from '@config/constants'
import apiRoots from 'src/config/apiRoots'
const {
  get
} = getRequestsByRoot({ root: apiRoots.familyRoot })

const { getErrorMsg, requestOptsWrapper } = serviceHocs

// const shareResponseKeys = { ...inkeCommonResponseKeys }
// const get = requestOptsWrapper(baseGet, shareResponseKeys)
// const post = requestOptsWrapper(basePost, shareResponseKeys)

class Apis {
  // 获取用户信息
  getUserInfo = get('api/v2/personal/user', {}, { autoLoading: false })
}

export default new Apis()
