// import '@babel/polyfill'
import './publicPath'
import React from 'react'
import ReactDOM from 'react-dom'
import { Provider } from 'react-redux'
import $loading from 'components/loading'
// import * as dd from 'dingtalk-jsapi'

import protoMix from './proto-mix'
// import utils from '../utils'
import '../styles/base.css'

import '../utils/protoExtension'
import './serviceIntercept'
import './wechat'
import { escapeWhenPrerendering } from '../config';
import userInit from './user-init'

const appContainer = document.getElementById('root')

/**
 * 统一在$loading.hide 这里做拦截, 保证预渲染时候 hide 失效, 页面始终有loading
 */
const _loadingHide = $loading.hide
Object.defineProperty($loading, 'hide', {
  enumerable: false,
  configurable: false,
  writable: false,
  value: escapeWhenPrerendering(_loadingHide)
})

/**
 * 注意: 只要你的页面有使用loading, 就一定要在具体页面的entry中引入这句话, 谨记, 否则预渲染之后会多个不消失loading
 */
export const loadingUntilFirstContentPaint = () => {
  $loading.show()

  // DOMContentLoaded 页面仍然是白的, 这里的200ms是 DOMContentLoaded -> First Contentful Paint 时间预估
  document.addEventListener('DOMContentLoaded', () => setTimeout($loading.hide, 200))
}

// if (!dd.other) {
//   dd.ready(function() {
//     // dd.ready参数为回调函数，在环境准备就绪时触发，jsapi的调用需要保证在该回调函数触发后调用，否则无效。
//     dd.biz.navigation.hideBar({
//       hidden: true,
//       onSuccess: function (res) {
//         console.log(res)
//       },
//       onFail: function(err) {
//         console.log(err)
//       }
//     })
//   })
// }

export default (App, Opts = {}) => {
  const { store = {} } = Opts

  const mixMap = {
    $store: store
  }
  userInit()
  protoMix(React.Component, mixMap)
  protoMix(React.PureComponent, mixMap)

  ReactDOM.render(
    <Provider store={store}>
      <App />
    </Provider>,
    appContainer
  )
}
