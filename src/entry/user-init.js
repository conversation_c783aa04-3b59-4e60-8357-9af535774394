import API from './apis'
import ticketReplace from './ticket-replace'
const ORIGIN = window.location.origin
const PATHNAME = window.location.pathname
const SERACH = window.location.search
const ssoService = encodeURIComponent(`${ORIGIN + PATHNAME + SERACH}`)
const userAgent = navigator.userAgent
const saveToken = () => {
  const localStroage = window.localStorage
  if (getUrlQuery('ticket')) {
    localStroage.setItem('TOKEN', getUrlQuery('ticket'))
    ticketReplace()
  }
}
const getUrlQuery = (name) => {
  const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`, 'i');
  const search = window.location.search.substring(1) || (window.location.href.split('?')[1] && window.location.href.split('?')[1].replace(/#!\/.+/, ''));
  if (!search) return false;
  const r = search.replace('#', '').match(reg);

  if (r != null) {
    // 对编码的字符串进行解码
    const unescapeStr = unescape(r[2]);
    switch (unescapeStr) {
      case 'true':
        return true;
      case 'null':
        return null;
      case 'false':
        return false;
      case 'undefined':
        return undefined;
      default:
        return unescapeStr;
    }
  } else {
    return null;
  }
}

const ua = (function () {
  const regs = {
    // 系统
    // 'ios': /iphone|ipad|ipod/,
    'android': /android/i,

    // 机型
    'iphone': /iphone/i,
    'ipad': /ipad/i,
    'ipod': /ipod/i,

    // 环境
    'weixin': /micromessenger/i,
    'mqq': /QQ\//i,
    'app': /inke/i,
    'alipay': /aliapp/i,
    'weibo': /weibo/i,

    // 浏览器
    'chrome': /chrome\//i
  };

  const ret = {}
  Object.keys(regs).forEach((key) => {
    var reg = regs[key]
    ret[key] = reg.test(userAgent)
  })

  ret.ios = ret.iphone || ret.ipad || ret.ipod;
  ret.mobile = ret.ios || ret.android;
  ret.pc = !ret.mobile;

  ret.chrome = !!window.chrome

  // 经过多次坑验证, 只有在同时有uid 和 sid的时候, 才可以认为是映客环境, 其他的ua等等判断都不靠谱, 虽然有些low, 但是最稳定
  // ret.isInke = !!(getUrlQuery('uid') && getUrlQuery('sid'))
  return ret;
})()

const getUserInfo = () => {
  API.getUserInfo().then(res => {
    window.localStorage.setItem('email', res.data.email)
    window.localStorage.setItem('user', JSON.stringify(res.data || {}))
  }, err => {
    console.error(err)
    // 认证失败
    if (err.status === 401) {
      if (window.location.hostname === 'localhost' || /^\d(.*\d)?$/.test(window.location.hostname)) { // localhost || 本地IP
        window.location.href = `http://sso.inkept.cn/?service=${ssoService}`
      } else {
        if (ua.pc) {
          if (window.decodeURIComponent(ssoService).indexOf('flow/index.html') !== -1) {
            window.location.href = 'https://workflow.inkept.cn/flow/16'
          } else {
            window.location.href = 'https://family.inkept.cn/order'
          }
        } else {
          window.location.href = `https://mobile.ikbase.cn/verify/sso/?service=${ssoService}`
        }
      }
    }
  })
}
export default () => {
  saveToken()
  getUserInfo()
}
