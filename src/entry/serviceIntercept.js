import axios from 'axios'
import axiosService from 'axios-service'
import $loading from 'components/loading'
import { compose } from 'redux'

const TIME_OUT = 30000

axiosService.init(axios, {
  // 基础设置
  defaults: {
    withCredentials: true
  },
  requestDefaults: {
    // response.data下面的配置
    // server端请求msg(
    msgKey: 'msg',
    // server端数据的key
    dataKey: 'data',
    // server端请求状态的key
    codeKey: 'status',
    // server端请求成功的状态, 注意: 此为response.data下该接口请求成功状态码, 非浏览器中http请求返回的成功状态(200)
    successCode: 0
  }
})

// 超时时间
axios.defaults.timeout = TIME_OUT

const errorLoadingHandle = error => {
  $loading.hide()
  return error
}

const enhanceErrorMsg = (message, config = {}) => `message: ${message}; params: ${JSON.stringify(config.params)}; data: ${JSON.stringify(config.data)}`

/**
 * 手动对api请求失败进行上报, 并将报错信息变的更详细
 * 所以统一走arms自带的接口上报, 这里不做错误处理, 同时避免多次上报, 在业务逻辑里和parseResponse自己处理
 *
 * @param {Object} error axios返回的Error信息
 */
const blReport = error => {
  let config
  if (window.__ikBl && error && (config = error.config)) {
    window.__ikBl.invoke('api', config.url, false, config.timeout || 0, -1, enhanceErrorMsg(error.message, error.config))
  }
  return error
}

const createComposedRejectHanlder = (...funcs) => compose(Promise.reject.bind(Promise), ...funcs)

const requestErrorHandler = createComposedRejectHanlder(errorLoadingHandle)

const responseErrorHandler = createComposedRejectHanlder(errorLoadingHandle)

const setTicketToHeader = config => {
  const { headers } = config;
  if (window.localStorage.getItem('mail')) {
    headers.mail = window.localStorage.getItem('mail')
    headers['Auth-Type'] = window.localStorage.getItem('Auth-Type')
  }
};

axios.interceptors.request.use(config => {
  config.params = {
    ...config.params,
    // _t: Date.now(),
    ticket: window.localStorage.getItem('TOKEN')
  }

  // undefined也会显示loading, 如果不想要loading 就给设置为false
  if (config.autoLoading === undefined || config.autoLoading === true) {
    // 保证timeout统一
    $loading.show({ timeout: axios.defaults.timeout })
  }
  setTicketToHeader(config)
  return config
}, requestErrorHandler)

axios.interceptors.response.use(config => {
  $loading.hide()
  return config
}, responseErrorHandler)
